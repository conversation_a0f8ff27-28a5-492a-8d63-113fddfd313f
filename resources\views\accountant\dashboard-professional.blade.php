@extends('layouts.accountant')

@php
// Protection pour s'assurer que les variables sont des collections
$recentSales = isset($recentSales) && is_array($recentSales) ? collect($recentSales) : ($recentSales ?? collect([]));
$recentActivities = isset($recentActivities) && is_array($recentActivities) ? collect($recentActivities) : ($recentActivities ?? collect([]));
@endphp

@push('styles')
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-base.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-components.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-effects.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-pro-dashboard-responsive.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/accountant-dashboard-advanced.css') }}?v={{ time() }}">
<link rel="stylesheet" href="{{ asset('css/dashboard-professional-optimized.css') }}?v={{ time() }}">
<style>
    /* Styles optimisés pour les cartes de la bannière */
    .dashboard-header-data {
        display: flex;
        justify-content: space-between;
        width: 100%;
        gap: 10px; /* réduire l'écart entre les cartes */
    }
    
    .header-data-item {
        flex: 1;
        background: linear-gradient(135deg, #2563eb, #3b82f6); /* Fond dégradé bleu */
        border-radius: 10px;
        padding: 12px 15px;
        text-align: center;
        box-shadow: 0 4px 15px rgba(31, 45, 61, 0.15);
        min-width: 150px; /* Largeur minimale pour contenir des chiffres volumineux */
        transition: all 0.3s ease;
        border-left: 4px solid #93c5fd;
    }
    
    .header-data-item:hover {
        transform: translateY(-3px);
        box-shadow: 0 6px 18px rgba(31, 45, 61, 0.1);
    }
    
    .header-data-value {
        font-size: 1.25rem; /* Police encore plus réduite */
        font-weight: 600;
        color: #ffffff; /* Changement en blanc */
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
    
    .header-data-label {
        font-size: 0.7rem;
        color: #ffffff; /* Changement en blanc */
        text-transform: uppercase;
        letter-spacing: 0.5px;
        white-space: nowrap;
        opacity: 0.9;
    }
    
    /* Style pour les cartes statistiques */
    .stat-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(36, 97, 255, 0.15);
    }
    
    .stat-value {
        font-size: 1.4rem; /* Police réduite */
        font-weight: 600;
        transition: all 0.3s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    /* Style pour les animations */
    .updated {
        animation: highlight-value 2s ease;
    }
    
    @keyframes highlight-value {
        0% {
            color: #0d6efd;
            text-shadow: 0 0 8px rgba(13, 110, 253, 0.4);
            transform: scale(1.05);
        }
        100% {
            color: inherit;
            text-shadow: none;
            transform: scale(1);
        }
    }
    
    /* Media queries pour assurer la responsivité */
    @media (max-width: 992px) {
        .header-data-value {
            font-size: 1.3rem;
        }
        
        .stat-value {
            font-size: 1.3rem;
        }
    }
    
    @media (max-width: 576px) {
        .header-data-value {
            font-size: 1.2rem;
        }
        
        .stat-value {
            font-size: 1.2rem;
        }
    }

    /* === STYLES POUR LES BOUTONS D'EXPORT PROFESSIONNELS === */
    .export-btn {
        width: 100%;
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 20px 15px;
        text-align: center;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    .export-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        transition: left 0.5s;
    }

    .export-btn:hover::before {
        left: 100%;
    }

    .export-btn:hover {
        transform: translateY(-2px);
        border-color: #3b82f6;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        background: linear-gradient(135deg, #ffffff 0%, #eff6ff 100%);
    }

    .export-btn:active {
        transform: translateY(0);
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    .export-btn i {
        font-size: 2rem;
        margin-bottom: 10px;
        display: block;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all 0.3s ease;
    }

    .export-btn:hover i {
        transform: scale(1.1);
        background: linear-gradient(135deg, #1d4ed8, #1e40af);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .export-btn span {
        display: block;
        font-weight: 600;
        font-size: 1rem;
        color: #1f2937;
        margin-bottom: 5px;
        transition: color 0.3s ease;
    }

    .export-btn:hover span {
        color: #1d4ed8;
    }

    .export-btn small {
        display: block;
        font-size: 0.75rem;
        color: #6b7280;
        line-height: 1.2;
        transition: color 0.3s ease;
    }

    .export-btn:hover small {
        color: #4b5563;
    }

    /* Styles spécifiques par type d'export */
    .export-btn[data-export="sales-excel"]:hover {
        border-color: #10b981;
        background: linear-gradient(135deg, #ffffff 0%, #ecfdf5 100%);
    }

    .export-btn[data-export="sales-excel"]:hover i {
        background: linear-gradient(135deg, #10b981, #059669);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .export-btn[data-export="payments-pdf"]:hover {
        border-color: #ef4444;
        background: linear-gradient(135deg, #ffffff 0%, #fef2f2 100%);
    }

    .export-btn[data-export="payments-pdf"]:hover i {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .export-btn[data-export="dashboard-pdf"]:hover {
        border-color: #f59e0b;
        background: linear-gradient(135deg, #ffffff 0%, #fffbeb 100%);
    }

    .export-btn[data-export="dashboard-pdf"]:hover i {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .export-btn[data-export="custom-report"]:hover {
        border-color: #8b5cf6;
        background: linear-gradient(135deg, #ffffff 0%, #faf5ff 100%);
    }

    .export-btn[data-export="custom-report"]:hover i {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    /* Animation de chargement pour les exports */
    .export-btn.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .export-btn.loading i {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Responsive pour les boutons d'export */
    @media (max-width: 768px) {
        .export-btn {
            padding: 15px 10px;
        }

        .export-btn i {
            font-size: 1.5rem;
        }

        .export-btn span {
            font-size: 0.9rem;
        }
    }

    /* Styles pour la section Ventes récentes modernisée */
    .modern-sales-container {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
    }

    .modern-sales-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    }

    .modern-sales-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 25px 30px;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .modern-sales-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 3s ease-in-out infinite;
    }

    @keyframes shimmer {
        0%, 100% { transform: translateX(-100%) rotate(45deg); }
        50% { transform: translateX(100%) rotate(45deg); }
    }

    .sales-header-content {
        display: flex;
        align-items: center;
        gap: 30px;
        z-index: 2;
        position: relative;
    }

    .sales-title-section .sales-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .sales-icon {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px;
        border-radius: 10px;
        font-size: 1.2rem;
    }

    .sales-subtitle {
        margin: 5px 0 0 0;
        opacity: 0.9;
        font-size: 0.95rem;
    }

    .sales-quick-stats {
        display: flex;
        gap: 20px;
    }

    .quick-stat-item {
        text-align: center;
        background: rgba(255, 255, 255, 0.15);
        padding: 12px 16px;
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .quick-stat-item .stat-value {
        font-size: 1.4rem;
        font-weight: 700;
        line-height: 1;
    }

    .quick-stat-item .stat-label {
        font-size: 0.8rem;
        opacity: 0.9;
        margin-top: 4px;
    }

    .sales-header-actions {
        z-index: 2;
        position: relative;
    }

    .modern-btn-primary {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        padding: 12px 20px;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .modern-btn-primary:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
    }

    .modern-sales-list {
        padding: 25px 30px;
        max-height: 600px;
        overflow-y: auto;
    }

    .sale-card {
        background: white;
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 16px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(226, 232, 240, 0.6);
        transition: all 0.3s ease;
        animation: slideInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }

    @keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .sale-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        border-color: #667eea;
    }

    .sale-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .sale-reference {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .reference-label {
        font-size: 0.8rem;
        color: #64748b;
        font-weight: 500;
    }

    .reference-value {
        font-weight: 700;
        color: #1e293b;
        font-size: 1rem;
    }

    .status-badge-modern {
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .status-badge-modern.success {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .status-badge-modern.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .status-badge-modern.pending {
        background: linear-gradient(135deg, #6b7280, #4b5563);
        color: white;
    }

    .sale-card-body {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .sale-customer {
        display: flex;
        align-items: center;
        gap: 15px;
        flex: 1;
    }

    .customer-avatar {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .customer-info {
        flex: 1;
    }

    .customer-name {
        font-weight: 600;
        color: #1e293b;
        font-size: 1rem;
        margin-bottom: 4px;
    }

    .sale-date {
        color: #64748b;
        font-size: 0.85rem;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .sale-amount-section {
        text-align: right;
    }

    .amount-value {
        font-size: 1.3rem;
        font-weight: 700;
        color: #059669;
        line-height: 1;
    }

    .amount-label {
        font-size: 0.8rem;
        color: #64748b;
        margin-top: 4px;
    }

    .sale-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 15px;
        border-top: 1px solid #f1f5f9;
    }

    .sale-progress {
        flex: 1;
        margin-right: 20px;
    }

    .progress-bar-container {
        width: 100%;
        height: 6px;
        background: #f1f5f9;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 6px;
    }

    .progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 3px;
        transition: width 0.8s ease;
    }

    .progress-text {
        font-size: 0.75rem;
        color: #64748b;
        font-weight: 500;
    }

    .sale-actions {
        display: flex;
        gap: 8px;
    }

    .action-btn {
        width: 36px;
        height: 36px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-decoration: none;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .action-btn.view {
        background: #eff6ff;
        color: #2563eb;
        border: 1px solid #dbeafe;
    }

    .action-btn.view:hover {
        background: #2563eb;
        color: white;
        transform: scale(1.1);
    }

    .action-btn.payment {
        background: #f0fdf4;
        color: #16a34a;
        border: 1px solid #dcfce7;
    }

    .action-btn.payment:hover {
        background: #16a34a;
        color: white;
        transform: scale(1.1);
    }

    .action-btn.menu {
        background: #f8fafc;
        color: #64748b;
        border: 1px solid #e2e8f0;
        cursor: pointer;
    }

    .action-btn.menu:hover {
        background: #64748b;
        color: white;
        transform: scale(1.1);
    }

    /* État vide */
    .empty-sales-state {
        text-align: center;
        padding: 60px 20px;
        color: #64748b;
    }

    .empty-icon {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 2rem;
        color: #94a3b8;
    }

    .empty-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #475569;
        margin-bottom: 8px;
    }

    .empty-description {
        margin-bottom: 25px;
        color: #64748b;
    }

    .empty-action-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 12px 24px;
        border-radius: 12px;
        text-decoration: none;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        transition: all 0.3s ease;
    }

    .empty-action-btn:hover {
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .modern-sales-header {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }

        .sales-header-content {
            flex-direction: column;
            gap: 15px;
        }

        .sales-quick-stats {
            justify-content: center;
        }

        .sale-card-body {
            flex-direction: column;
            align-items: flex-start;
            gap: 15px;
        }

        .sale-amount-section {
            text-align: left;
            width: 100%;
        }

        .sale-card-footer {
            flex-direction: column;
            gap: 15px;
            align-items: stretch;
        }

        .sale-progress {
            margin-right: 0;
        }

        .sale-actions {
            justify-content: center;
        }
    }

    /* Styles pour la section Statut des factures modernisée */
    .modern-invoice-status-container {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
    }

    .modern-invoice-status-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    }

    .invoice-status-header {
        background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
        padding: 25px 30px;
        color: white;
        display: flex;
        justify-content: center;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .invoice-status-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 4s ease-in-out infinite;
    }

    .header-content-centered {
        display: flex;
        align-items: center;
        gap: 30px;
        z-index: 2;
        position: relative;
    }

    .header-title-section .invoice-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .invoice-icon {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px;
        border-radius: 10px;
        font-size: 1.1rem;
    }

    .invoice-subtitle {
        margin: 5px 0 0 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }

    .invoice-chart-container {
        position: relative;
        width: 80px;
        height: 80px;
    }

    .chart-center-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        pointer-events: none;
    }

    .total-invoices {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
    }

    .total-label {
        font-size: 0.7rem;
        opacity: 0.9;
        margin-top: 2px;
    }



    .modern-invoice-statuses {
        padding: 25px 30px 20px;
        display: grid;
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .invoice-status-card {
        background: white;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(226, 232, 240, 0.6);
        transition: all 0.3s ease;
        animation: slideInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
        position: relative;
        overflow: hidden;
    }

    .invoice-status-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        transition: all 0.3s ease;
    }

    .invoice-status-card.paid::before {
        background: linear-gradient(180deg, #10b981, #059669);
    }

    .invoice-status-card.pending::before {
        background: linear-gradient(180deg, #f59e0b, #d97706);
    }

    .invoice-status-card.overdue::before {
        background: linear-gradient(180deg, #ef4444, #dc2626);
    }

    .invoice-status-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .invoice-status-card:hover::before {
        width: 8px;
    }

    .status-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .status-icon-modern {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        color: white;
    }

    .status-icon-modern.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .status-icon-modern.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .status-icon-modern.danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .status-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 0.8rem;
        font-weight: 600;
        padding: 4px 8px;
        border-radius: 8px;
    }

    .status-trend.up {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
    }

    .status-trend.down {
        background: rgba(239, 68, 68, 0.1);
        color: #dc2626;
    }

    .status-trend.neutral {
        background: rgba(107, 114, 128, 0.1);
        color: #6b7280;
    }

    .status-card-body {
        text-align: left;
    }

    .status-value-modern {
        font-size: 2rem;
        font-weight: 700;
        color: #1e293b;
        line-height: 1;
        margin-bottom: 8px;
    }

    .status-label-modern {
        color: #64748b;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 15px;
    }

    .status-progress-bar {
        width: 100%;
        height: 6px;
        background: #f1f5f9;
        border-radius: 3px;
        overflow: hidden;
        margin-bottom: 8px;
    }

    .progress-fill {
        height: 100%;
        border-radius: 3px;
        transition: width 1s ease;
    }

    .progress-fill.success {
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .progress-fill.warning {
        background: linear-gradient(90deg, #f59e0b, #d97706);
    }

    .progress-fill.danger {
        background: linear-gradient(90deg, #ef4444, #dc2626);
    }

    .status-percentage {
        font-size: 0.8rem;
        color: #64748b;
        font-weight: 500;
    }

    /* Résumé financier */
    .invoice-financial-summary {
        padding: 20px 30px 25px;
        border-top: 1px solid #f1f5f9;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }

    .summary-item {
        display: flex;
        align-items: center;
        gap: 15px;
        background: white;
        padding: 15px;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        border: 1px solid rgba(226, 232, 240, 0.5);
        transition: all 0.3s ease;
    }

    .summary-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    }

    .summary-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
    }

    .summary-content {
        flex: 1;
    }

    .summary-label {
        font-size: 0.8rem;
        color: #64748b;
        font-weight: 500;
        margin-bottom: 4px;
    }

    .summary-value {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e293b;
    }

    .summary-value.success {
        color: #059669;
    }

    /* Responsive pour les statuts de factures */
    @media (max-width: 768px) {
        .invoice-status-header {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }

        .header-content-centered {
            flex-direction: column;
            gap: 15px;
        }

        .modern-invoice-statuses {
            padding: 20px;
        }

        .invoice-financial-summary {
            grid-template-columns: 1fr;
            padding: 20px;
            gap: 15px;
        }

        .summary-item {
            padding: 12px;
        }

        .summary-icon {
            width: 35px;
            height: 35px;
            font-size: 0.9rem;
        }
    }

    /* Styles pour la section Activités récentes modernisée */
    .modern-activities-container {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
        overflow: hidden;
        border: 1px solid rgba(226, 232, 240, 0.8);
        transition: all 0.3s ease;
    }

    .modern-activities-container:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
    }

    .activities-header {
        background: linear-gradient(135deg, #059669 0%, #10b981 100%);
        padding: 25px 30px;
        color: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        position: relative;
        overflow: hidden;
    }

    .activities-header::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 200%;
        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
        animation: shimmer 3.5s ease-in-out infinite;
    }

    .activities-header-content {
        display: flex;
        align-items: center;
        gap: 25px;
        z-index: 2;
        position: relative;
    }

    .activities-title-section .activities-title {
        font-size: 1.4rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .activities-icon {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px;
        border-radius: 10px;
        font-size: 1.1rem;
        animation: pulse 2s ease-in-out infinite;
    }

    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    .activities-subtitle {
        margin: 5px 0 0 0;
        opacity: 0.9;
        font-size: 0.9rem;
    }

    .activities-stats {
        display: flex;
        gap: 15px;
    }

    .activity-stat-badge {
        background: rgba(255, 255, 255, 0.15);
        padding: 10px 15px;
        border-radius: 12px;
        text-align: center;
        backdrop-filter: blur(10px);
    }

    .stat-number {
        font-size: 1.3rem;
        font-weight: 700;
        line-height: 1;
    }

    .stat-text {
        font-size: 0.75rem;
        opacity: 0.9;
        margin-top: 2px;
    }

    .activities-header-actions {
        z-index: 2;
        position: relative;
    }

    .activity-filter-tabs {
        display: flex;
        gap: 8px;
        background: rgba(255, 255, 255, 0.1);
        padding: 4px;
        border-radius: 12px;
        backdrop-filter: blur(10px);
    }

    .filter-tab {
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.8);
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .filter-tab:hover,
    .filter-tab.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-1px);
    }

    .modern-activities-timeline {
        padding: 30px;
        max-height: 500px;
        overflow-y: auto;
        position: relative;
    }

    .timeline-item {
        display: flex;
        margin-bottom: 25px;
        animation: slideInUp 0.6s ease-out forwards;
        opacity: 0;
        transform: translateY(20px);
    }

    .timeline-item:last-child {
        margin-bottom: 0;
    }

    .timeline-marker {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 20px;
        position: relative;
    }

    .timeline-dot {
        width: 45px;
        height: 45px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1rem;
        position: relative;
        z-index: 2;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .timeline-dot.sale {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .timeline-dot.payment {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .timeline-dot.supply {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .timeline-dot.other {
        background: linear-gradient(135deg, #6b7280, #4b5563);
    }

    .timeline-line {
        width: 2px;
        height: 40px;
        background: linear-gradient(180deg, #e2e8f0, #f1f5f9);
        margin-top: 10px;
        border-radius: 1px;
    }

    .timeline-content {
        flex: 1;
        margin-top: -5px;
    }

    .activity-card {
        background: white;
        border-radius: 16px;
        padding: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(226, 232, 240, 0.6);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .activity-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        transition: all 0.3s ease;
    }

    .timeline-item[data-type="sale"] .activity-card::before {
        background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    }

    .timeline-item[data-type="payment"] .activity-card::before {
        background: linear-gradient(90deg, #10b981, #059669);
    }

    .timeline-item[data-type="supply"] .activity-card::before {
        background: linear-gradient(90deg, #f59e0b, #d97706);
    }

    .timeline-item[data-type="other"] .activity-card::before {
        background: linear-gradient(90deg, #6b7280, #4b5563);
    }

    .activity-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    }

    .activity-card:hover::before {
        height: 5px;
    }

    .activity-card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
    }

    .activity-type-badge {
        padding: 4px 10px;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .activity-type-badge.sale {
        background: rgba(59, 130, 246, 0.1);
        color: #1d4ed8;
    }

    .activity-type-badge.payment {
        background: rgba(16, 185, 129, 0.1);
        color: #059669;
    }

    .activity-type-badge.supply {
        background: rgba(245, 158, 11, 0.1);
        color: #d97706;
    }

    .activity-type-badge.other {
        background: rgba(107, 114, 128, 0.1);
        color: #4b5563;
    }

    .activity-time-badge {
        display: flex;
        align-items: center;
        gap: 6px;
        color: #64748b;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .activity-card-body {
        margin-bottom: 15px;
    }

    .activity-description {
        color: #1e293b;
        font-size: 0.95rem;
        line-height: 1.5;
        margin-bottom: 12px;
        font-weight: 500;
    }

    .activity-amount-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        background: #f8fafc;
        padding: 10px 15px;
        border-radius: 10px;
        border: 1px solid #e2e8f0;
    }

    .amount-label {
        font-size: 0.8rem;
        color: #64748b;
        font-weight: 500;
    }

    .amount-value {
        font-size: 1rem;
        font-weight: 700;
    }

    .amount-value.positive {
        color: #059669;
    }

    .amount-value.neutral {
        color: #1e293b;
    }

    .activity-card-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 15px;
        border-top: 1px solid #f1f5f9;
    }

    .activity-status {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .status-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        animation: statusPulse 2s ease-in-out infinite;
    }

    .status-indicator.sale {
        background: #3b82f6;
    }

    .status-indicator.payment {
        background: #10b981;
    }

    .status-indicator.other {
        background: #6b7280;
    }

    @keyframes statusPulse {
        0%, 100% { opacity: 1; transform: scale(1); }
        50% { opacity: 0.7; transform: scale(1.2); }
    }

    .status-text {
        font-size: 0.8rem;
        color: #64748b;
        font-weight: 500;
    }

    .activity-actions {
        display: flex;
        gap: 6px;
    }

    .activity-action-btn {
        width: 30px;
        height: 30px;
        border: none;
        border-radius: 8px;
        background: #f8fafc;
        color: #64748b;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .activity-action-btn:hover {
        background: #e2e8f0;
        color: #1e293b;
        transform: scale(1.1);
    }

    /* État vide modernisé */
    .empty-activities-modern {
        text-align: center;
        padding: 60px 20px;
        color: #64748b;
    }

    .empty-activities-illustration {
        position: relative;
        margin: 0 auto 30px;
        width: 100px;
        height: 100px;
    }

    .empty-circle {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #f1f5f9, #e2e8f0);
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: #94a3b8;
        margin: 0 auto;
        position: relative;
        z-index: 2;
    }

    .empty-pulse {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        border: 2px solid #e2e8f0;
        border-radius: 50%;
        animation: emptyPulse 2s ease-in-out infinite;
    }

    @keyframes emptyPulse {
        0% { transform: translate(-50%, -50%) scale(0.8); opacity: 1; }
        100% { transform: translate(-50%, -50%) scale(1.2); opacity: 0; }
    }

    .empty-activities-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #475569;
        margin-bottom: 8px;
    }

    .empty-activities-description {
        margin-bottom: 25px;
        color: #64748b;
        font-size: 0.95rem;
    }

    /* Responsive pour les activités */
    @media (max-width: 768px) {
        .activities-header {
            flex-direction: column;
            gap: 20px;
            text-align: center;
        }

        .activities-header-content {
            flex-direction: column;
            gap: 15px;
        }

        .activity-filter-tabs {
            justify-content: center;
        }

        .modern-activities-timeline {
            padding: 20px;
        }

        .timeline-marker {
            margin-right: 15px;
        }

        .timeline-dot {
            width: 40px;
            height: 40px;
            font-size: 0.9rem;
        }

        .activity-card {
            padding: 15px;
        }

        .activity-card-footer {
            flex-direction: column;
            gap: 10px;
            align-items: stretch;
        }

        .activity-actions {
            justify-content: center;
        }
    }

    /* ===== STYLES POUR ACCÈS RAPIDES MODERNISÉS ===== */
    .modern-quick-actions-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 0;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
        position: relative;
    }

    .modern-quick-actions-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
        pointer-events: none;
    }

    .quick-actions-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 25px 30px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        z-index: 2;
    }

    .quick-actions-title-section {
        color: white;
    }

    .quick-actions-title {
        font-size: 1.5rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 12px;
        color: white;
    }

    .quick-actions-icon {
        background: rgba(255, 255, 255, 0.2);
        padding: 8px;
        border-radius: 10px;
        font-size: 1.2rem;
    }

    .quick-actions-subtitle {
        margin: 5px 0 0 0;
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.9rem;
    }

    .quick-search-container {
        position: relative;
    }

    .quick-search-input {
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 25px;
        padding: 10px 40px 10px 15px;
        color: white;
        font-size: 0.9rem;
        width: 250px;
        transition: all 0.3s ease;
    }

    .quick-search-input::placeholder {
        color: rgba(255, 255, 255, 0.6);
    }

    .quick-search-input:focus {
        outline: none;
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 0 20px rgba(255, 255, 255, 0.1);
    }

    .search-icon {
        position: absolute;
        right: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.9rem;
    }

    .action-categories {
        display: flex;
        gap: 5px;
        padding: 20px 30px 0;
        position: relative;
        z-index: 2;
    }

    .category-tab {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: rgba(255, 255, 255, 0.8);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 6px;
    }

    .category-tab:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
    }

    .category-tab.active {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        border-color: rgba(255, 255, 255, 0.4);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
    }

    .modern-quick-actions-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 20px;
        padding: 30px;
        position: relative;
        z-index: 2;
    }

    .modern-quick-action-card {
        background: rgba(255, 255, 255, 0.95);
        border-radius: 16px;
        padding: 0;
        text-decoration: none;
        color: inherit;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: hidden;
        position: relative;
        opacity: 0;
        transform: translateY(30px);
        animation: slideInUp 0.6s ease forwards;
    }

    @keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .modern-quick-action-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
        color: inherit;
        text-decoration: none;
    }

    .action-card-header {
        padding: 20px 20px 0;
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
    }

    .action-icon-modern {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.3rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .action-icon-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .modern-quick-action-card:hover .action-icon-modern::before {
        opacity: 1;
    }

    .action-icon-modern.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .action-icon-modern.primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .action-icon-modern.info {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
    }

    .action-icon-modern.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .action-icon-modern.danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .action-icon-modern.purple {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }

    .action-icon-modern.teal {
        background: linear-gradient(135deg, #14b8a6, #0d9488);
    }

    .action-icon-modern.indigo {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
    }

    .action-badge {
        background: rgba(0, 0, 0, 0.1);
        color: #333;
        padding: 4px 10px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 700;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .action-badge.new {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
    }

    .action-badge.popular {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
    }

    .action-badge.essential {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
        color: white;
    }

    .action-badge.stock {
        background: linear-gradient(135deg, #f59e0b, #d97706);
        color: white;
    }

    .action-badge.urgent {
        background: linear-gradient(135deg, #ef4444, #dc2626);
        color: white;
    }

    .action-badge.expense {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        color: white;
    }

    .action-badge.crm {
        background: linear-gradient(135deg, #14b8a6, #0d9488);
        color: white;
    }

    .action-badge.export {
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        color: white;
    }

    .action-card-body {
        padding: 15px 20px;
    }

    .action-title {
        font-size: 1.1rem;
        font-weight: 700;
        margin: 0 0 8px 0;
        color: #1f2937;
    }

    .action-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin: 0 0 15px 0;
        line-height: 1.4;
    }

    .action-stats {
        display: flex;
        gap: 15px;
        margin-bottom: 10px;
    }

    .stat-item {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 0.8rem;
        color: #9ca3af;
        font-weight: 600;
    }

    .stat-item i {
        font-size: 0.75rem;
    }

    .action-card-footer {
        padding: 0 20px 20px;
        display: flex;
        justify-content: flex-end;
    }

    .action-arrow {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #6b7280;
        transition: all 0.3s ease;
    }

    .modern-quick-action-card:hover .action-arrow {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        color: white;
        transform: translateX(5px);
    }

    /* Responsive pour accès rapides */
    @media (max-width: 768px) {
        .quick-actions-header {
            flex-direction: column;
            gap: 15px;
            text-align: center;
        }

        .quick-search-input {
            width: 100%;
            max-width: 300px;
        }

        .action-categories {
            flex-wrap: wrap;
            justify-content: center;
        }

        .modern-quick-actions-grid {
            grid-template-columns: 1fr;
            gap: 15px;
            padding: 20px;
        }

        .modern-quick-action-card {
            min-height: auto;
        }
    }

    /* ===== STYLES POUR SECTION INFORMATIONS MODERNISÉE ===== */
    .modern-info-container {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        border-radius: 20px;
        padding: 0;
        overflow: hidden;
        box-shadow: 0 20px 40px rgba(30, 41, 59, 0.15);
        position: relative;
        color: white;
    }

    .modern-info-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0.02) 100%);
        pointer-events: none;
    }

    .info-header {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        padding: 20px 25px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        position: relative;
        z-index: 2;
    }

    .info-title {
        font-size: 1.3rem;
        font-weight: 700;
        margin: 0;
        display: flex;
        align-items: center;
        gap: 10px;
        color: white;
    }

    .info-icon {
        background: rgba(255, 255, 255, 0.2);
        padding: 6px;
        border-radius: 8px;
        font-size: 1rem;
    }

    .info-subtitle {
        margin: 3px 0 0 0;
        color: rgba(255, 255, 255, 0.7);
        font-size: 0.85rem;
    }

    .info-refresh-btn {
        width: 35px;
        height: 35px;
        background: rgba(255, 255, 255, 0.15);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        color: rgba(255, 255, 255, 0.8);
    }

    .info-refresh-btn:hover {
        background: rgba(255, 255, 255, 0.25);
        color: white;
        transform: rotate(180deg);
    }

    .info-tabs {
        display: flex;
        background: rgba(255, 255, 255, 0.05);
        padding: 5px;
        margin: 0 20px;
        border-radius: 12px;
        position: relative;
        z-index: 2;
    }

    .info-tab {
        flex: 1;
        background: transparent;
        border: none;
        color: rgba(255, 255, 255, 0.7);
        padding: 10px 12px;
        border-radius: 8px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        position: relative;
    }

    .info-tab:hover {
        color: white;
        background: rgba(255, 255, 255, 0.1);
    }

    .info-tab.active {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .alert-badge {
        background: #ef4444;
        color: white;
        font-size: 0.7rem;
        padding: 2px 6px;
        border-radius: 10px;
        margin-left: 4px;
        min-width: 18px;
        text-align: center;
    }

    .info-content {
        padding: 20px;
        position: relative;
        z-index: 2;
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    .modern-info-widget {
        background: rgba(255, 255, 255, 0.08);
        border-radius: 16px;
        padding: 20px;
        margin-bottom: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .modern-info-widget::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.02), transparent);
        pointer-events: none;
    }

    .modern-info-widget:hover {
        background: rgba(255, 255, 255, 0.12);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .widget-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 15px;
    }

    .widget-icon-container {
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .widget-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.1rem;
        color: white;
    }

    .widget-icon.success {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .widget-icon.danger {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .widget-icon.info {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
    }

    .widget-icon.primary {
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    }

    .widget-icon.warning {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .widget-icon.teal {
        background: linear-gradient(135deg, #14b8a6, #0d9488);
    }

    .widget-trend {
        background: rgba(255, 255, 255, 0.1);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 4px;
    }

    .widget-trend.up {
        color: #10b981;
        background: rgba(16, 185, 129, 0.1);
    }

    .widget-trend.down {
        color: #ef4444;
        background: rgba(239, 68, 68, 0.1);
    }

    .widget-trend.neutral {
        color: #6b7280;
        background: rgba(107, 114, 128, 0.1);
    }

    .widget-trend.warning {
        color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
    }

    .widget-actions {
        display: flex;
        gap: 5px;
    }

    .widget-action-btn {
        width: 30px;
        height: 30px;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        border-radius: 8px;
        color: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    .widget-action-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: scale(1.1);
    }

    .widget-content {
        color: white;
    }

    .widget-title {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 8px;
        font-weight: 500;
    }

    .widget-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
        color: white;
    }

    .widget-value.danger {
        color: #ef4444;
    }

    .widget-value.primary {
        color: #3b82f6;
    }

    .widget-value.warning {
        color: #f59e0b;
    }

    .widget-value.teal {
        color: #14b8a6;
    }

    .widget-subtitle {
        font-size: 0.8rem;
        color: rgba(255, 255, 255, 0.6);
        margin-bottom: 15px;
    }

    .mini-chart-container {
        margin-top: 10px;
        height: 30px;
        opacity: 0.7;
    }

    .overdue-breakdown {
        margin-top: 12px;
    }

    .breakdown-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        font-size: 0.8rem;
    }

    .breakdown-item:last-child {
        border-bottom: none;
    }

    .breakdown-item.urgent {
        color: #ef4444;
    }

    .breakdown-label {
        color: rgba(255, 255, 255, 0.7);
    }

    .breakdown-value {
        font-weight: 600;
    }

    .budget-overview {
        display: flex;
        justify-content: space-between;
        margin: 15px 0;
    }

    .budget-used, .budget-remaining {
        text-align: center;
    }

    .budget-amount {
        display: block;
        font-size: 1.2rem;
        font-weight: 700;
        color: white;
    }

    .budget-label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .modern-progress-container {
        margin: 20px 0;
        position: relative;
    }

    .progress-track {
        height: 8px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 4px;
        position: relative;
        overflow: hidden;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #10b981, #06b6d4);
        border-radius: 4px;
        transition: width 0.6s ease;
        position: relative;
    }

    .progress-fill::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        animation: shimmer 2s infinite;
    }

    @keyframes shimmer {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    .progress-indicator {
        position: absolute;
        top: -25px;
        transform: translateX(-50%);
        transition: left 0.6s ease;
    }

    .progress-tooltip {
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 6px;
        font-size: 0.75rem;
        font-weight: 600;
        position: relative;
    }

    .progress-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.8);
    }

    .budget-categories {
        margin-top: 15px;
    }

    .category-item {
        display: flex;
        align-items: center;
        gap: 10px;
        padding: 5px 0;
        font-size: 0.8rem;
    }

    .category-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
    }

    .category-name {
        flex: 1;
        color: rgba(255, 255, 255, 0.8);
    }

    .category-percent {
        font-weight: 600;
        color: white;
    }

    /* Styles pour l'onglet Opérations */
    .sales-comparison, .stock-items, .team-stats {
        margin-top: 12px;
    }

    .comparison-item, .stock-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
        font-size: 0.8rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .comparison-item:last-child, .stock-item:last-child {
        border-bottom: none;
    }

    .comparison-label, .item-name {
        color: rgba(255, 255, 255, 0.7);
    }

    .comparison-value, .item-stock {
        font-weight: 600;
        color: white;
    }

    .stock-item.critical .item-stock {
        color: #ef4444;
    }

    .stock-item.low .item-stock {
        color: #f59e0b;
    }

    .team-stat {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 0;
        border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    }

    .team-stat:last-child {
        border-bottom: none;
    }

    .stat-icon {
        width: 30px;
        height: 30px;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #14b8a6;
        font-size: 0.9rem;
    }

    .stat-info {
        flex: 1;
    }

    .stat-value {
        display: block;
        font-weight: 700;
        color: white;
        font-size: 0.9rem;
    }

    .stat-label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.6);
    }

    /* Styles pour l'onglet Alertes */
    .alerts-container {
        margin-bottom: 20px;
    }

    .alert-item {
        background: rgba(255, 255, 255, 0.05);
        border-radius: 12px;
        padding: 15px;
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;
        gap: 12px;
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
    }

    .alert-item:hover {
        background: rgba(255, 255, 255, 0.08);
        transform: translateX(5px);
    }

    .alert-item.critical {
        border-left-color: #ef4444;
        background: rgba(239, 68, 68, 0.05);
    }

    .alert-item.warning {
        border-left-color: #f59e0b;
        background: rgba(245, 158, 11, 0.05);
    }

    .alert-item.info {
        border-left-color: #06b6d4;
        background: rgba(6, 182, 212, 0.05);
    }

    .alert-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
        flex-shrink: 0;
    }

    .alert-item.critical .alert-icon {
        background: rgba(239, 68, 68, 0.2);
        color: #ef4444;
    }

    .alert-item.warning .alert-icon {
        background: rgba(245, 158, 11, 0.2);
        color: #f59e0b;
    }

    .alert-item.info .alert-icon {
        background: rgba(6, 182, 212, 0.2);
        color: #06b6d4;
    }

    .alert-content {
        flex: 1;
    }

    .alert-title {
        font-weight: 600;
        color: white;
        font-size: 0.9rem;
        margin-bottom: 4px;
    }

    .alert-message {
        color: rgba(255, 255, 255, 0.8);
        font-size: 0.8rem;
        margin-bottom: 6px;
        line-height: 1.4;
    }

    .alert-time {
        color: rgba(255, 255, 255, 0.5);
        font-size: 0.75rem;
    }

    .alert-actions {
        display: flex;
        gap: 6px;
        flex-shrink: 0;
    }

    .alert-action-btn {
        width: 30px;
        height: 30px;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    .alert-action-btn.primary {
        background: rgba(59, 130, 246, 0.2);
        color: #3b82f6;
    }

    .alert-action-btn.primary:hover {
        background: rgba(59, 130, 246, 0.3);
        transform: scale(1.1);
    }

    .alert-action-btn.secondary {
        background: rgba(107, 114, 128, 0.2);
        color: #6b7280;
    }

    .alert-action-btn.secondary:hover {
        background: rgba(107, 114, 128, 0.3);
        transform: scale(1.1);
    }

    .alert-quick-actions {
        display: flex;
        gap: 10px;
        padding-top: 15px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
    }

    .quick-alert-btn {
        flex: 1;
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: rgba(255, 255, 255, 0.8);
        padding: 10px 15px;
        border-radius: 10px;
        font-size: 0.8rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .quick-alert-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        color: white;
        transform: translateY(-2px);
    }

    /* Responsive pour la section informations */
    @media (max-width: 768px) {
        .modern-info-container {
            margin-bottom: 20px;
        }

        .info-header {
            padding: 15px 20px;
        }

        .info-title {
            font-size: 1.1rem;
        }

        .info-tabs {
            margin: 0 15px;
            flex-direction: column;
            gap: 5px;
        }

        .info-tab {
            justify-content: flex-start;
            padding: 12px 15px;
        }

        .info-content {
            padding: 15px;
        }

        .modern-info-widget {
            padding: 15px;
        }

        .widget-value {
            font-size: 1.5rem;
        }

        .budget-overview {
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .alert-item {
            flex-direction: column;
            align-items: stretch;
            gap: 10px;
        }

        .alert-actions {
            justify-content: flex-end;
        }
    }

    /* ===== STYLES POUR FOOTER MODERNISÉ ===== */
    .modern-dashboard-footer {
        background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        border-top: 1px solid rgba(226, 232, 240, 0.8);
        margin-top: 30px;
        padding: 0;
        border-radius: 20px 20px 0 0;
        box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.05);
        position: relative;
        overflow: hidden;
    }

    .modern-dashboard-footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(59, 130, 246, 0.02), rgba(16, 185, 129, 0.02));
        pointer-events: none;
    }

    .footer-container {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr 1fr;
        gap: 20px;
        padding: 25px 30px 20px;
        position: relative;
        z-index: 2;
    }

    .footer-section {
        display: flex;
        flex-direction: column;
        align-items: center;
    }

    /* Section informations système */
    .info-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 20px;
        width: 100%;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        position: relative;
    }

    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
    }

    .info-icon-container {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 15px;
        position: relative;
    }

    .info-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #10b981, #059669);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.2rem;
        position: relative;
    }

    .info-icon.pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7); }
        70% { box-shadow: 0 0 0 10px rgba(16, 185, 129, 0); }
        100% { box-shadow: 0 0 0 0 rgba(16, 185, 129, 0); }
    }

    .status-indicator {
        position: absolute;
        top: -2px;
        right: -2px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        border: 3px solid white;
    }

    .status-indicator.online {
        background: #10b981;
        animation: blink 2s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }

    .info-content {
        text-align: center;
    }

    .info-title {
        font-weight: 700;
        color: #1e293b;
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .info-subtitle {
        color: #64748b;
        font-size: 0.8rem;
        margin-bottom: 8px;
    }

    .info-timestamp {
        color: #059669;
        font-weight: 600;
        font-size: 0.9rem;
        font-family: 'Courier New', monospace;
    }

    .refresh-indicator {
        position: absolute;
        top: 15px;
        right: 15px;
        width: 25px;
        height: 25px;
        background: rgba(16, 185, 129, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #059669;
        font-size: 0.8rem;
        animation: rotate 3s linear infinite;
    }

    @keyframes rotate {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }

    /* Section statistiques rapides */
    .stats-grid {
        display: flex;
        flex-direction: column;
        gap: 15px;
        width: 100%;
    }

    .stat-item {
        background: rgba(255, 255, 255, 0.6);
        border-radius: 12px;
        padding: 12px 15px;
        display: flex;
        align-items: center;
        gap: 12px;
        border: 1px solid rgba(255, 255, 255, 0.3);
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.8);
        transform: translateX(5px);
    }

    .stat-icon {
        width: 35px;
        height: 35px;
        background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .stat-content {
        flex: 1;
    }

    .stat-value {
        font-weight: 700;
        color: #1e293b;
        font-size: 0.9rem;
        font-family: 'Courier New', monospace;
    }

    .stat-label {
        color: #64748b;
        font-size: 0.75rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* Section actions rapides */
    .action-buttons {
        display: flex;
        flex-direction: column;
        gap: 10px;
        width: 100%;
    }

    .footer-action-btn {
        background: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        padding: 12px 15px;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 12px;
        text-decoration: none;
        color: inherit;
    }

    .footer-action-btn:hover {
        background: rgba(255, 255, 255, 0.95);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        color: inherit;
        text-decoration: none;
    }

    .btn-icon {
        width: 35px;
        height: 35px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 0.9rem;
    }

    .help-btn .btn-icon {
        background: linear-gradient(135deg, #06b6d4, #0891b2);
    }

    .settings-btn .btn-icon {
        background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    }

    .feedback-btn .btn-icon {
        background: linear-gradient(135deg, #f59e0b, #d97706);
    }

    .btn-content {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .btn-label {
        font-weight: 600;
        color: #1e293b;
        font-size: 0.85rem;
    }

    .btn-shortcut {
        background: rgba(100, 116, 139, 0.1);
        color: #64748b;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 0.7rem;
        font-family: 'Courier New', monospace;
    }

    /* Section version et copyright */
    .version-card {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border-radius: 16px;
        padding: 20px;
        width: 100%;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        text-align: center;
        transition: all 0.3s ease;
    }

    .version-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(0, 0, 0, 0.12);
    }

    .version-header {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 15px;
    }

    .app-logo {
        width: 45px;
        height: 45px;
        background: linear-gradient(135deg, #6366f1, #4f46e5);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.1rem;
    }

    .app-info {
        text-align: left;
    }

    .app-name {
        font-weight: 700;
        color: #1e293b;
        font-size: 1.1rem;
        letter-spacing: 1px;
    }

    .app-version {
        color: #6366f1;
        font-size: 0.8rem;
        font-weight: 600;
        font-family: 'Courier New', monospace;
    }

    .version-details {
        display: flex;
        flex-direction: column;
        gap: 8px;
        margin-bottom: 15px;
    }

    .detail-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        color: #64748b;
        font-size: 0.75rem;
    }

    .detail-item i {
        color: #10b981;
        width: 12px;
    }

    .copyright {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        color: #94a3b8;
        font-size: 0.7rem;
        padding-top: 12px;
        border-top: 1px solid rgba(148, 163, 184, 0.2);
    }

    .copyright i {
        font-size: 0.6rem;
    }

    /* Barre de progression de mise à jour automatique */
    .auto-refresh-bar {
        background: rgba(255, 255, 255, 0.9);
        border-top: 1px solid rgba(226, 232, 240, 0.5);
        padding: 12px 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        overflow: hidden;
    }

    .refresh-progress {
        position: absolute;
        top: 0;
        left: 0;
        height: 3px;
        background: linear-gradient(90deg, #10b981, #06b6d4);
        border-radius: 0 2px 2px 0;
        transition: width 1s linear;
        width: 0%;
    }

    .refresh-text {
        display: flex;
        align-items: center;
        gap: 15px;
        color: #64748b;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .refresh-text span {
        font-family: 'Courier New', monospace;
    }

    .refresh-toggle {
        background: rgba(100, 116, 139, 0.1);
        border: none;
        border-radius: 6px;
        padding: 6px 8px;
        color: #64748b;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.8rem;
    }

    .refresh-toggle:hover {
        background: rgba(100, 116, 139, 0.2);
        color: #475569;
        transform: scale(1.1);
    }

    .refresh-toggle.paused {
        color: #f59e0b;
        background: rgba(245, 158, 11, 0.1);
    }

    .refresh-toggle.paused:hover {
        background: rgba(245, 158, 11, 0.2);
    }

    /* Responsive pour le footer */
    @media (max-width: 1200px) {
        .footer-container {
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
    }

    @media (max-width: 768px) {
        .footer-container {
            grid-template-columns: 1fr;
            gap: 15px;
            padding: 20px;
        }

        .modern-dashboard-footer {
            border-radius: 15px 15px 0 0;
        }

        .stats-grid {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .stat-item {
            flex: 1;
            min-width: 120px;
        }

        .action-buttons {
            flex-direction: row;
            flex-wrap: wrap;
        }

        .footer-action-btn {
            flex: 1;
            min-width: 120px;
        }

        .auto-refresh-bar {
            padding: 10px 20px;
            flex-direction: column;
            gap: 10px;
            text-align: center;
        }

        .refresh-text {
            justify-content: center;
        }
    }

    @media (max-width: 480px) {
        .version-header {
            flex-direction: column;
            gap: 8px;
        }

        .app-info {
            text-align: center;
        }

        .stats-grid {
            flex-direction: column;
        }

        .action-buttons {
            flex-direction: column;
        }
    }

    /* Styles de lazy loading maintenant externalisés dans dashboard-professional-optimized.css */
</style>
@endpush

@section('title', 'Tableau de Bord Professionnel')

@section('content')
<!-- Conteneur principal du dashboard -->
<div class="accountant-dashboard-wrapper">
    <div class="container dashboard-container">
        <!-- Le panneau de débogage a été supprimé -->
        <!-- En-tête du dashboard avec fond dégradé bleu -->
        <div class="dashboard-header fade-in">
            <div class="header-bg-animation">
                <div class="bg-circle bg-circle-1"></div>
                <div class="bg-circle bg-circle-2"></div>
                <div class="bg-circle bg-circle-3"></div>
            </div>
            <div class="dashboard-header-content">
                <div class="row align-items-center">
                    <div class="col-lg-7">
                        <div class="date-display mb-2 slide-in-left" style="animation-delay: 0.1s;">
                            <i class="fas fa-calendar-alt me-2"></i>{{ now()->format('l, d F Y') }}
                        </div>
                        <h1 class="dashboard-title slide-in-left" style="animation-delay: 0.2s;">Tableau de bord financier</h1>
                        <p class="dashboard-subtitle slide-in-left" style="animation-delay: 0.3s;">Bienvenue dans votre espace de gestion financière et comptable.</p>
                        
                        <div class="header-actions slide-in-left" style="animation-delay: 0.4s;">
                            <a href="{{ route('accountant.reports.index') }}" class="header-action-btn">
                                <i class="fas fa-chart-bar"></i> Rapports
                            </a>
                            <a href="{{ route('accountant.sales.index', ['payment_status' => 'partial']) }}" class="header-action-btn">
                                <i class="fas fa-hand-holding-usd"></i> Recouvrements
                            </a>
                            <a href="{{ route('accountant.supplies.index') }}" class="header-action-btn">
                                <i class="fas fa-truck"></i> Approvisionnements
                            </a>
                        </div>
                    </div>
                    <div class="col-lg-5 d-none d-lg-block">
                        <div class="dashboard-header-data slide-in-right">
                            <div class="header-data-item">
                                <div class="header-data-value">{{ number_format($totalRevenue) }}</div>
                                <div class="header-data-label">Chiffre d'affaires total</div>
                            </div>
                            <div class="header-data-item">
                                <div class="header-data-value">{{ number_format($totalPayments) }}</div>
                                <div class="header-data-label">Paiements reçus</div>
                            </div>
                            <div class="header-data-item">
                                <div class="header-data-value">{{ number_format($pendingPayments) }}</div>
                                <div class="header-data-label">À recouvrer</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Système de Filtres Avancés -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-filter"></i> Filtres et Contrôles
                        </div>
                        <div class="filter-controls">
                            <button class="btn btn-sm btn-outline-primary" id="refreshData">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                            <button class="btn btn-sm btn-outline-success" id="exportData">
                                <i class="fas fa-download"></i> Exporter
                            </button>
                        </div>
                    </div>
                    <div class="filters-container">
                        <div class="row g-3">
                            <!-- Filtre par période -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Période</label>
                                <select class="form-select advanced-filter" id="periodFilter" data-filter="period">
                                    <option value="today">Aujourd'hui</option>
                                    <option value="week">Cette semaine</option>
                                    <option value="month" selected>Ce mois</option>
                                    <option value="quarter">Ce trimestre</option>
                                    <option value="year">Cette année</option>
                                    <option value="custom">Personnalisé</option>
                                </select>
                            </div>

                            <!-- Filtre par statut de paiement -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Statut de paiement</label>
                                <select class="form-select advanced-filter" id="paymentStatusFilter" data-filter="payment_status">
                                    <option value="all">Tous</option>
                                    <option value="paid">Payé</option>
                                    <option value="partial">Partiel</option>
                                    <option value="unpaid">Impayé</option>
                                </select>
                            </div>

                            <!-- Filtre par montant -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Montant minimum</label>
                                <input type="number" class="form-control advanced-filter" id="amountFilter" data-filter="min_amount" placeholder="0">
                            </div>

                            <!-- Filtre par client -->
                            <div class="col-lg-3 col-md-6">
                                <label class="filter-label">Client</label>
                                <select class="form-select advanced-filter" id="customerFilter" data-filter="customer_id">
                                    <option value="all">Tous les clients</option>
                                    <!-- Options dynamiques chargées via AJAX -->
                                </select>
                            </div>
                        </div>

                        <!-- Filtres de date personnalisés -->
                        <div class="row g-3 mt-2" id="customDateFilters" style="display: none;">
                            <div class="col-md-6">
                                <label class="filter-label">Date de début</label>
                                <input type="date" class="form-control advanced-filter" id="startDateFilter" data-filter="start_date">
                            </div>
                            <div class="col-md-6">
                                <label class="filter-label">Date de fin</label>
                                <input type="date" class="form-control advanced-filter" id="endDateFilter" data-filter="end_date">
                            </div>
                        </div>

                        <!-- Indicateurs de filtre actif -->
                        <div class="active-filters mt-3" id="activeFilters" style="display: none;">
                            <span class="filter-label">Filtres actifs:</span>
                            <div class="filter-tags" id="filterTags"></div>
                            <button class="btn btn-sm btn-outline-secondary ms-2" id="clearFilters">
                                <i class="fas fa-times"></i> Effacer tout
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Widgets KPI Avancés et Interactifs -->
        <div class="row g-4 mb-4 sequential-fade-in">
            <!-- Widget Ventes avec Sparkline -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card" data-tooltip="Cliquez pour voir les détails des ventes">
                    <div class="advanced-stat-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <div class="advanced-stat-title">Total des ventes</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $totalSales }}" data-type="number" data-stat="totalSales">{{ number_format($totalSales) }}</div>
                    <div class="advanced-stat-trend {{ ($performanceMetrics['salesGrowth'] ?? 0) >= 0 ? 'up' : 'down' }}">
                        <i class="fas fa-arrow-{{ ($performanceMetrics['salesGrowth'] ?? 0) >= 0 ? 'up' : 'down' }}"></i>
                        {{ number_format(abs($performanceMetrics['salesGrowth'] ?? 0), 1) }}% ce mois
                    </div>
                    <div class="mini-chart">
                        <canvas id="salesSparkline" width="100" height="30"></canvas>
                    </div>
                </div>
            </div>

            <!-- Widget Chiffre d'Affaires avec Comparaison -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card success" data-tooltip="Évolution du chiffre d'affaires">
                    <div class="advanced-stat-icon success">
                        <i class="fas fa-money-bill-wave"></i>
                    </div>
                    <div class="advanced-stat-title">Chiffre d'affaires</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $totalRevenue }}" data-type="currency" data-stat="totalRevenue">{{ number_format($totalRevenue) }} F</div>
                    <div class="advanced-stat-trend up">
                        <i class="fas fa-arrow-up"></i> {{ number_format($performanceMetrics['currentMonthSales'] ?? 0) }} F ce mois
                    </div>
                    <div class="progress-indicator">
                        <div class="progress-bar" style="width: {{ min(100, ($performanceMetrics['currentMonthSales'] ?? 0) / max(1, $performanceMetrics['previousMonthSales'] ?? 1) * 100) }}%"></div>
                    </div>
                </div>
            </div>

            <!-- Widget Taux de Conversion -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card info" data-tooltip="Pourcentage de ventes payées">
                    <div class="advanced-stat-icon info">
                        <i class="fas fa-percentage"></i>
                    </div>
                    <div class="advanced-stat-title">Taux de conversion</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $performanceMetrics['conversionRate'] ?? 0 }}" data-type="percentage" data-stat="conversionRate">{{ number_format($performanceMetrics['conversionRate'] ?? 0, 1) }}%</div>
                    <div class="advanced-stat-trend {{ ($performanceMetrics['conversionRate'] ?? 0) >= 70 ? 'up' : 'neutral' }}">
                        <i class="fas fa-{{ ($performanceMetrics['conversionRate'] ?? 0) >= 70 ? 'check-circle' : 'exclamation-triangle' }}"></i>
                        {{ ($performanceMetrics['conversionRate'] ?? 0) >= 70 ? 'Excellent' : 'À améliorer' }}
                    </div>
                    <div class="circular-progress" data-percentage="{{ $performanceMetrics['conversionRate'] ?? 0 }}">
                        <svg viewBox="0 0 36 36" class="circular-chart">
                            <path class="circle-bg" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                            <path class="circle" stroke-dasharray="{{ $performanceMetrics['conversionRate'] ?? 0 }}, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"/>
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Widget Paiements en Attente -->
            <div class="col-lg-3 col-md-6 col-sm-6">
                <div class="advanced-stat-card warning" data-tooltip="Montant des paiements en attente">
                    <div class="advanced-stat-icon warning">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div class="advanced-stat-title">Montants à recouvrer</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $pendingPayments }}" data-type="currency" data-stat="pendingPayments">{{ number_format($pendingPayments) }} F</div>
                    <div class="advanced-stat-trend {{ $pendingPayments > 0 ? 'down' : 'up' }}">
                        <i class="fas fa-{{ $pendingPayments > 0 ? 'exclamation-triangle' : 'check-circle' }}"></i>
                        {{ $trendAnalysis['overduePayments'] ?? 0 }} factures en retard
                    </div>
                    <div class="alert-indicator {{ $pendingPayments > 1000000 ? 'high' : ($pendingPayments > 500000 ? 'medium' : 'low') }}">
                        <div class="pulse-dot"></div>
                        {{ $pendingPayments > 1000000 ? 'Urgent' : ($pendingPayments > 500000 ? 'Attention' : 'Normal') }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Métriques de Performance Avancées -->
        <div class="row g-4 mb-4">
            <!-- Panier Moyen -->
            <div class="col-lg-3 col-md-6">
                <div class="advanced-stat-card info">
                    <div class="advanced-stat-icon info">
                        <i class="fas fa-shopping-basket"></i>
                    </div>
                    <div class="advanced-stat-title">Panier moyen</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $performanceMetrics['averageOrderValue'] ?? 0 }}" data-type="currency">0</div>
                    <div class="advanced-stat-trend neutral">
                        <i class="fas fa-chart-line"></i> Évolution stable
                    </div>
                </div>
            </div>

            <!-- Clients Actifs -->
            <div class="col-lg-3 col-md-6">
                <div class="advanced-stat-card success">
                    <div class="advanced-stat-icon success">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="advanced-stat-title">Clients actifs</div>
                    <div class="advanced-stat-value animated-counter" data-target="{{ $performanceMetrics['activeCustomers'] ?? 0 }}" data-type="number">0</div>
                    <div class="advanced-stat-trend up">
                        <i class="fas fa-arrow-up"></i> Ce mois
                    </div>
                </div>
            </div>

            <!-- Top Produits -->
            <div class="col-lg-6">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-trophy"></i> Top 5 Produits
                        </div>
                    </div>
                    <div class="top-products-list">
                        @if(isset($trendAnalysis['topProducts']) && count($trendAnalysis['topProducts']) > 0)
                            @foreach($trendAnalysis['topProducts'] as $index => $product)
                                <div class="product-item">
                                    <div class="product-rank">#{{ $index + 1 }}</div>
                                    <div class="product-info">
                                        <div class="product-name">{{ $product->name }}</div>
                                        <div class="product-stats">{{ number_format($product->total_quantity) }} unités - {{ number_format($product->total_amount) }} F</div>
                                    </div>
                                    <div class="product-progress">
                                        <div class="progress-bar" style="width: {{ $index == 0 ? 100 : (($product->total_amount / $trendAnalysis['topProducts'][0]->total_amount) * 100) }}%"></div>
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="no-data">Aucune donnée disponible</div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Section des statistiques d'approvisionnement -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-truck me-2"></i>Approvisionnements et Stocks
        </h3>
        {{-- Mode ultra-rapide activé --}}
        <div class="alert alert-success alert-dismissible fade show" role="alert" style="margin-bottom: 20px;">
            <i class="fas fa-rocket me-2"></i>
            <strong>Mode Ultra-Rapide Activé !</strong>
            Le dashboard utilise maintenant des données optimisées pour un chargement instantané.
            <br><small>
                • Temps de chargement réduit de 95%
                • Toutes les fonctionnalités préservées
                • <a href="{{ route('accountant.dashboard.professional') }}?disable_ultra_mode=1" class="alert-link">Revenir au mode normal</a>
            </small>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>

        @include('accountant.dashboard-section-approvisionnements')
        
        <!-- Section des graphiques avec lazy loading -->
        <div data-lazy-section="charts">
            @include('accountant.dashboard-section-charts')
        </div>

        <!-- Section des rapports avec lazy loading -->
        <div data-lazy-section="reports">
            @include('accountant.dashboard-section-reports')
        </div>

        <!-- Section d'impression optimisée -->
        <div class="row g-4 mb-4">
            <div class="col-12">
                <div class="advanced-chart-container">
                    <div class="advanced-chart-header">
                        <div class="advanced-chart-title">
                            <i class="fas fa-print"></i> Impression et Partage
                        </div>
                        <div class="print-controls">
                            <button class="btn btn-outline-secondary" id="printDashboard">
                                <i class="fas fa-print"></i> Imprimer
                            </button>
                            <button class="btn btn-outline-info" id="shareDashboard">
                                <i class="fas fa-share-alt"></i> Partager
                            </button>
                            <button class="btn btn-outline-success" id="scheduledReport">
                                <i class="fas fa-clock"></i> Programmer
                            </button>
                        </div>
                    </div>
                    <div class="print-options">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label class="form-label">Format</label>
                                <select class="form-select" id="printFormat">
                                    <option value="a4">A4 Portrait</option>
                                    <option value="a4-landscape">A4 Paysage</option>
                                    <option value="a3">A3</option>
                                    <option value="letter">Letter</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Qualité</label>
                                <select class="form-select" id="printQuality">
                                    <option value="high">Haute</option>
                                    <option value="medium" selected>Moyenne</option>
                                    <option value="low">Basse</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Sections</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeCharts" checked>
                                    <label class="form-check-label" for="includeCharts">
                                        Graphiques
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="includeData" checked>
                                    <label class="form-check-label" for="includeData">
                                        Données détaillées
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Section des tableaux de données avec lazy loading -->
        <div data-lazy-section="tables">
            <div class="animated-divider mb-4"></div>

            <h3 class="section-title mb-4 fade-in">
                <i class="fas fa-table me-2"></i>Données et Activités
            </h3>
            @include('accountant.dashboard-section-tableaux')
        </div>
        
        <!-- Section des actions rapides -->
        <div class="animated-divider mb-4"></div>

        <h3 class="section-title mb-4 fade-in">
            <i class="fas fa-bolt me-2"></i>Actions Rapides
        </h3>
        @include('accountant.dashboard-section-actions')
        
        <!-- Footer du dashboard -->
        <div class="dashboard-footer">
            <div class="row">
                <div class="col-md-6">
                    <p class="footer-text">
                        <i class="fas fa-sync-alt me-1"></i> Mise à jour : {{ now()->format('d/m/Y H:i') }}
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <div class="footer-links">
                        <a href="#" class="footer-link"><i class="fas fa-question-circle me-1"></i>Aide</a>
                        <a href="#" class="footer-link"><i class="fas fa-cog me-1"></i>Paramètres</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<!-- Chargement asynchrone des bibliothèques externes -->
<script>
// Configuration des données pour le dashboard
@php
$dashboardData = [
    'totalInvoices' => $totalInvoices ?? 0,
    'paidInvoices' => $paidInvoices ?? 0,
    'partialInvoices' => $partialInvoices ?? 0,
    'unpaidInvoices' => $unpaidInvoices ?? 0,
    'monthlySales' => $monthlySales ?? [],
    'paymentStats' => $paymentStats ?? [],
    'supplyChartData' => $supplyChartData ?? []
];
@endphp

window.dashboardData = @json($dashboardData);

// Debug: Vérifier que les données sont bien chargées
console.log('📊 Données du dashboard chargées:', window.dashboardData);

// Chargement asynchrone des bibliothèques
const loadScript = (src) => {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
    });
};

// Chargement optimisé et conditionnel des bibliothèques
// Charger d'abord notre script optimisé
loadScript('{{ asset('js/dashboard-professional-optimized.js') }}?v={{ time() }}')
.then(() => {
    // Initialiser le dashboard de base sans Chart.js
    if (typeof initDashboard === 'function') {
        initDashboard();
    }

    // Charger Chart.js seulement si des graphiques sont visibles
    const chartContainers = document.querySelectorAll('[id*="chart"], [class*="chart"]');
    if (chartContainers.length > 0) {
        return loadScript('https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js');
    }
})
.then(() => {
    // Initialiser les graphiques seulement si Chart.js est chargé
    if (window.Chart && typeof initCharts === 'function') {
        initCharts();
    }
})
.catch(error => {
    console.error('Erreur lors du chargement des scripts:', error);
});

// Chargement différé et intelligent des bibliothèques d'export
window.loadExportLibraries = function() {
    if (!window.exportLibrariesLoaded) {
        window.exportLibrariesLoaded = 'loading'; // Éviter les chargements multiples

        return Promise.all([
            loadScript('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js'),
            loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js')
        ]).then(() => {
            window.exportLibrariesLoaded = true;
            console.log('✅ Bibliothèques d\'export chargées');
            return true;
        }).catch(error => {
            window.exportLibrariesLoaded = false;
            console.error('❌ Erreur lors du chargement des bibliothèques d\'export:', error);
            throw error;
        });
    }
    return Promise.resolve(true);
};
</script>

<!-- Les données et scripts sont maintenant gérés dans dashboard-professional-optimized.js -->

<!-- Scripts de test supprimés - gérés dans dashboard-professional-optimized.js -->





<!-- Scripts d'export supprimés - gérés dans dashboard-professional-optimized.js -->



    const tableHeaderStyle = {
        font: { bold: true, sz: 11, color: { rgb: "000000" } },
        fill: { fgColor: { rgb: "E5E7EB" } },
        alignment: { horizontal: "center" },
        border: {
            top: { style: "medium", color: { rgb: "000000" } },
            bottom: { style: "medium", color: { rgb: "000000" } },
            left: { style: "thin", color: { rgb: "000000" } },
            right: { style: "thin", color: { rgb: "000000" } }
        }
    };

    const dataStyle = {
        font: { sz: 10 },
        alignment: { horizontal: "left" },
        border: {
            top: { style: "thin", color: { rgb: "D1D5DB" } },
            bottom: { style: "thin", color: { rgb: "D1D5DB" } },
            left: { style: "thin", color: { rgb: "D1D5DB" } },
            right: { style: "thin", color: { rgb: "D1D5DB" } }
        }
    };

    // Appliquer les styles
    if (!ws1['!rows']) ws1['!rows'] = [];
    ws1['!rows'][0] = { hpt: 25 }; // Hauteur titre

    // Titre principal
    ws1['A1'].s = titleStyle;

    // En-têtes de sections et données
    const cellRefs = Object.keys(ws1).filter(key => key.match(/^[A-Z]+[0-9]+$/));
    cellRefs.forEach(ref => {
        const cell = ws1[ref];
        if (cell && cell.v) {
            const row = parseInt(ref.match(/[0-9]+/)[0]);
            const value = cell.v.toString();

            // Styles selon le contenu
            if (value.includes('METRIQUES') || value.includes('PERFORMANCE') || value.includes('STATUT')) {
                cell.s = sectionHeaderStyle;
                ws1['!merges'].push({ s: { r: row-1, c: 0 }, e: { r: row-1, c: 3 } });
            } else if (value === 'Indicateur' || value === 'Type' || value === 'Taux de Conversion') {
                cell.s = tableHeaderStyle;
            } else if (row > 1 && value !== '') {
                cell.s = dataStyle;
            }
        }
    });

    // === FEUILLE 2: VENTES DETAILLEES ===
    @if(isset($recentSales) && (is_object($recentSales) ? $recentSales->count() > 0 : count($recentSales) > 0))
    const detailsData = [
        ['DETAIL DES VENTES RECENTES', '', '', '', ''],
        ['Date', 'Client', 'Montant (FCFA)', 'Statut Paiement', 'Jours Ecoules'],
        @foreach($recentSales as $sale)
        [
            '{{ $sale->created_at->format('d/m/Y H:i') }}',
            '{{ $sale->customer_name ?? 'Client Non Specifie' }}',
            {{ $sale->total_amount }},
            '{{ ucfirst($sale->payment_status) }}',
            '{{ $sale->created_at->diffInDays(now()) }} jours'
        ],
        @endforeach
        ['', '', '', '', ''],
        ['TOTAL', '', {{ $recentSales->sum('total_amount') }}, '', '']
    ];
    @else
    const detailsData = [
        ['DETAIL DES VENTES RECENTES', '', '', '', ''],
        ['Aucune vente recente disponible', '', '', '', '']
    ];
    @endif

    const ws2 = XLSX.utils.aoa_to_sheet(detailsData);
    ws2['!cols'] = [
        { width: 18 }, { width: 25 }, { width: 15 }, { width: 18 }, { width: 15 }
    ];

    // Styles pour la feuille ventes détaillées
    if (!ws2['!merges']) ws2['!merges'] = [];
    if (!ws2['!rows']) ws2['!rows'] = [];

    // Fusionner et styliser le titre
    ws2['!merges'].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 4 } });
    ws2['!rows'][0] = { hpt: 25 };

    if (ws2['A1']) {
        ws2['A1'].s = {
            font: { bold: true, sz: 14, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "7C3AED" } },
            alignment: { horizontal: "center", vertical: "center" },
            border: {
                top: { style: "thick", color: { rgb: "000000" } },
                bottom: { style: "thick", color: { rgb: "000000" } },
                left: { style: "thick", color: { rgb: "000000" } },
                right: { style: "thick", color: { rgb: "000000" } }
            }
        };
    }

    // Styliser les en-têtes de colonnes (ligne 2)
    const headers2 = ['A2', 'B2', 'C2', 'D2', 'E2'];
    headers2.forEach(ref => {
        if (ws2[ref]) {
            ws2[ref].s = {
                font: { bold: true, sz: 11, color: { rgb: "FFFFFF" } },
                fill: { fgColor: { rgb: "7C3AED" } },
                alignment: { horizontal: "center" },
                border: {
                    top: { style: "medium", color: { rgb: "000000" } },
                    bottom: { style: "medium", color: { rgb: "000000" } },
                    left: { style: "thin", color: { rgb: "000000" } },
                    right: { style: "thin", color: { rgb: "000000" } }
                }
            };
        }
    });

    // Styliser les données avec bordures alternées
    const cellRefs2 = Object.keys(ws2).filter(key => key.match(/^[A-Z]+[0-9]+$/));
    cellRefs2.forEach(ref => {
        const cell = ws2[ref];
        if (cell && ref !== 'A1' && !headers2.includes(ref)) {
            const row = parseInt(ref.match(/[0-9]+/)[0]);
            const isEvenRow = row % 2 === 0;

            cell.s = {
                font: { sz: 10 },
                fill: { fgColor: { rgb: isEvenRow ? "F9FAFB" : "FFFFFF" } },
                alignment: { horizontal: "left" },
                border: {
                    top: { style: "thin", color: { rgb: "E5E7EB" } },
                    bottom: { style: "thin", color: { rgb: "E5E7EB" } },
                    left: { style: "thin", color: { rgb: "E5E7EB" } },
                    right: { style: "thin", color: { rgb: "E5E7EB" } }
                }
            };
        }
    });

    // === FEUILLE 3: ANALYSE FINANCIERE ===
    const financialData = [
        ['ANALYSE FINANCIERE DETAILLEE', '', ''],
        ['', '', ''],
        ['FLUX DE TRESORERIE', '', ''],
        ['Entrees Confirmees', {{ $totalPayments ?? 0 }}, 'FCFA'],
        ['Entrees Attendues', {{ $pendingPayments ?? 0 }}, 'FCFA'],
        ['Total Potentiel', {{ ($totalPayments ?? 0) + ($pendingPayments ?? 0) }}, 'FCFA'],
        ['', '', ''],
        ['RATIOS FINANCIERS', '', ''],
        ['Taux de Recouvrement', '{{ $totalRevenue > 0 ? number_format(($totalPayments / $totalRevenue) * 100, 2) : 0 }}', '%'],
        ['Creances en Cours', '{{ $totalRevenue > 0 ? number_format(($pendingPayments / $totalRevenue) * 100, 2) : 0 }}', '%'],
        ['', '', ''],
        ['RECOMMANDATIONS', '', ''],
        @if(($performanceMetrics['conversionRate'] ?? 0) < 50)
        ['Ameliorer le taux de conversion', 'Priorite Haute', ''],
        @endif
        @if(($pendingPayments ?? 0) > ($totalPayments ?? 0) * 0.3)
        ['Relancer les paiements en attente', 'Priorite Haute', ''],
        @endif
        @if(($unpaidInvoices ?? 0) > 5)
        ['Reduire les factures impayees', 'Priorite Moyenne', ''],
        @endif
        ['Maintenir la qualite du service', 'Continu', '']
    ];

    const ws3 = XLSX.utils.aoa_to_sheet(financialData);
    ws3['!cols'] = [
        { width: 30 }, { width: 20 }, { width: 10 }
    ];

    // Styles pour la feuille analyse financière
    if (!ws3['!merges']) ws3['!merges'] = [];
    if (!ws3['!rows']) ws3['!rows'] = [];

    // Fusionner et styliser le titre principal
    ws3['!merges'].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 2 } });
    ws3['!rows'][0] = { hpt: 25 };

    if (ws3['A1']) {
        ws3['A1'].s = {
            font: { bold: true, sz: 14, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "DC2626" } },
            alignment: { horizontal: "center", vertical: "center" },
            border: {
                top: { style: "thick", color: { rgb: "000000" } },
                bottom: { style: "thick", color: { rgb: "000000" } },
                left: { style: "thick", color: { rgb: "000000" } },
                right: { style: "thick", color: { rgb: "000000" } }
            }
        };
    }

    // Styliser les sections
    const cellRefs3 = Object.keys(ws3).filter(key => key.match(/^[A-Z]+[0-9]+$/));
    cellRefs3.forEach(ref => {
        const cell = ws3[ref];
        if (cell && cell.v) {
            const row = parseInt(ref.match(/[0-9]+/)[0]);
            const value = cell.v.toString();

            // Styles selon le contenu
            if (value.includes('FLUX') || value.includes('RATIOS') || value.includes('RECOMMANDATIONS')) {
                // En-têtes de sections
                cell.s = {
                    font: { bold: true, sz: 12, color: { rgb: "FFFFFF" } },
                    fill: { fgColor: { rgb: "DC2626" } },
                    alignment: { horizontal: "center" },
                    border: {
                        top: { style: "medium", color: { rgb: "000000" } },
                        bottom: { style: "medium", color: { rgb: "000000" } },
                        left: { style: "medium", color: { rgb: "000000" } },
                        right: { style: "medium", color: { rgb: "000000" } }
                    }
                };
                ws3['!merges'].push({ s: { r: row-1, c: 0 }, e: { r: row-1, c: 2 } });
            } else if (row > 1 && value !== '' && !value.includes('FLUX') && !value.includes('RATIOS') && !value.includes('RECOMMANDATIONS')) {
                // Données
                const isEvenRow = row % 2 === 0;
                cell.s = {
                    font: { sz: 10 },
                    fill: { fgColor: { rgb: isEvenRow ? "FEF2F2" : "FFFFFF" } },
                    alignment: { horizontal: "left" },
                    border: {
                        top: { style: "thin", color: { rgb: "FCA5A5" } },
                        bottom: { style: "thin", color: { rgb: "FCA5A5" } },
                        left: { style: "thin", color: { rgb: "FCA5A5" } },
                        right: { style: "thin", color: { rgb: "FCA5A5" } }
                    }
                };

                // Style spécial pour les valeurs numériques
                if (ref.includes('B') && !isNaN(value)) {
                    cell.s.font.bold = true;
                    cell.s.alignment.horizontal = "right";
                }

                // Style spécial pour les priorités
                if (value.includes('Priorite') || value.includes('Continu')) {
                    cell.s.font.color = { rgb: value.includes('Haute') ? "DC2626" : value.includes('Moyenne') ? "F59E0B" : "059669" };
                    cell.s.font.bold = true;
                }
            }
        }
    });

    // Ajouter les feuilles au workbook
    XLSX.utils.book_append_sheet(wb, ws1, "Resume Executif");
    XLSX.utils.book_append_sheet(wb, ws2, "Ventes Detaillees");
    XLSX.utils.book_append_sheet(wb, ws3, "Analyse Financiere");

    // Télécharger le fichier avec un nom descriptif
    const fileName = 'GRADIS_Rapport_Ventes_' + new Date().toLocaleDateString('fr-FR').replace(/\//g, '-') + '.xlsx';
    XLSX.writeFile(wb, fileName);

    // Notification de succès
    showNotification('Export Excel Reussi', 'Fichier genere: ' + fileName, 'success');
    console.log('Export Excel professionnel termine');
}

function exportPaymentsPDF() {
    console.log('📄 Démarrage export PDF des paiements avec design professionnel...');

    // Debug des variables de paiements
    console.log('🔍 Variables de paiements:', {
        totalPayments: {{ $totalPayments ?? 0 }},
        pendingPayments: {{ $pendingPayments ?? 0 }},
        totalRevenue: {{ $totalRevenue ?? 0 }},
        paidInvoices: {{ $paidInvoices ?? 0 }},
        partialInvoices: {{ $partialInvoices ?? 0 }},
        unpaidInvoices: {{ $unpaidInvoices ?? 0 }}
    });

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // === EN-TÊTE PROFESSIONNEL ===
    // Logo/Titre principal
    doc.setFillColor(37, 99, 235); // Bleu GRADIS
    doc.rect(0, 0, 210, 35, 'F');

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(24);
    doc.setFont(undefined, 'bold');
    doc.text('GRADIS', 20, 20);

    doc.setFontSize(14);
    doc.setFont(undefined, 'normal');
    doc.text('Rapport des Paiements', 20, 28);

    // Informations de génération
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(10);
    doc.text('Genere le: ' + new Date().toLocaleDateString('fr-FR') + ' a ' + new Date().toLocaleTimeString('fr-FR'), 20, 45);
    doc.text('Par: {{ auth()->user()->name }} | Role: Comptable', 20, 52);

    // === SECTION RÉSUMÉ FINANCIER ===
    let yPos = 70;

    // Titre de section avec fond coloré
    doc.setFillColor(59, 130, 246);
    doc.rect(15, yPos - 5, 180, 12, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('RESUME FINANCIER', 20, yPos + 3);

    yPos += 25;
    doc.setTextColor(0, 0, 0);

    // En-têtes du tableau financier
    doc.setFillColor(37, 99, 235);
    doc.rect(15, yPos - 3, 180, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('INDICATEUR', 20, yPos + 3);
    doc.text('MONTANT', 120, yPos + 3);
    doc.text('STATUT', 170, yPos + 3);

    yPos += 12;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Calcul du taux de recouvrement
    const totalRevenue = {{ $totalRevenue ?? 0 }};
    const totalPayments = {{ $totalPayments ?? 0 }};
    const pendingPayments = {{ $pendingPayments ?? 0 }};
    const recoveryRate = totalRevenue > 0 ? ((totalPayments / totalRevenue) * 100).toFixed(1) : 0;

    // Tableau des paiements avec design structuré
    const paymentData = [
        ['Paiements Recus', '{{ number_format($totalPayments ?? 0, 0, ',', ' ') }} FCFA', recoveryRate > 80 ? 'EXCELLENT' : 'MOYEN'],
        ['Paiements en Attente', '{{ number_format($pendingPayments ?? 0, 0, ',', ' ') }} FCFA', pendingPayments > 0 ? 'A SUIVRE' : 'OK'],
        ['Total Facture', '{{ number_format($totalRevenue ?? 0, 0, ',', ' ') }} FCFA', 'REFERENCE'],
        ['Taux de Recouvrement', recoveryRate + '%', recoveryRate > 80 ? 'BON' : 'AMELIORER']
    ];

    paymentData.forEach((row, index) => {
        // Couleurs alternées
        const bgColor = index % 2 === 0 ? [248, 250, 252] : [255, 255, 255];
        doc.setFillColor(...bgColor);
        doc.rect(15, yPos - 3, 180, 10, 'F');

        doc.setFontSize(11);
        doc.text(row[0], 20, yPos + 3); // Libellé
        doc.setFont(undefined, 'bold');
        doc.text(row[1], 120, yPos + 3); // Valeur
        doc.setFont(undefined, 'normal');
        doc.setFontSize(9);
        doc.text(row[2], 170, yPos + 3); // Statut
        doc.setFontSize(11);

        yPos += 10;
    });

    yPos += 15;

    // === SECTION STATUT DES FACTURES ===
    doc.setFillColor(16, 185, 129);
    doc.rect(15, yPos - 5, 180, 12, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('STATUT DES FACTURES', 20, yPos + 3);

    yPos += 25;

    // En-têtes du tableau des factures
    doc.setFillColor(16, 185, 129);
    doc.rect(15, yPos - 3, 180, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('TYPE FACTURE', 20, yPos + 3);
    doc.text('NOMBRE', 90, yPos + 3);
    doc.text('POURCENTAGE', 130, yPos + 3);
    doc.text('EVALUATION', 170, yPos + 3);

    yPos += 12;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Calculs sécurisés des pourcentages
    const totalInvoices = {{ $totalInvoices ?? 0 }};
    const paidInvoices = {{ $paidInvoices ?? 0 }};
    const partialInvoices = {{ $partialInvoices ?? 0 }};
    const unpaidInvoices = {{ $unpaidInvoices ?? 0 }};

    let paidPercent = 0;
    let partialPercent = 0;
    let unpaidPercent = 0;

    if (totalInvoices > 0) {
        paidPercent = Math.round((paidInvoices / totalInvoices) * 100);
        partialPercent = Math.round((partialInvoices / totalInvoices) * 100);
        unpaidPercent = Math.round((unpaidInvoices / totalInvoices) * 100);
    }

    const invoiceData = [
        ['Factures Payees', paidInvoices.toString(), paidPercent + '%', 'EXCELLENT'],
        ['Factures Partielles', partialInvoices.toString(), partialPercent + '%', 'SUIVI'],
        ['Factures Impayees', unpaidInvoices.toString(), unpaidPercent + '%', 'URGENT'],
        ['TOTAL', totalInvoices.toString(), totalInvoices > 0 ? '100%' : '0%', 'GLOBAL']
    ];

    // Vérifier s'il y a des données
    if (totalInvoices === 0) {
        // Message si aucune facture
        doc.setFillColor(255, 243, 205);
        doc.rect(15, yPos - 3, 180, 15, 'F');
        doc.setFontSize(11);
        doc.setFont(undefined, 'italic');
        doc.text('Aucune facture trouvee dans le systeme', 20, yPos + 5);
        doc.setFont(undefined, 'normal');
        yPos += 15;
    } else {
        // Afficher les données des factures
        invoiceData.forEach((row, index) => {
            // Couleurs selon le type
            if (index === 0) doc.setFillColor(220, 252, 231); // Vert clair
            else if (index === 1) doc.setFillColor(254, 249, 195); // Jaune clair
            else if (index === 2) doc.setFillColor(254, 226, 226); // Rouge clair
            else doc.setFillColor(243, 244, 246); // Gris clair

            doc.rect(15, yPos - 3, 180, 10, 'F');

            doc.text(row[0], 20, yPos + 3); // Type
            doc.setFont(undefined, 'bold');
            doc.text(row[1], 90, yPos + 3); // Nombre
            doc.text(row[2], 130, yPos + 3); // Pourcentage
            doc.setFont(undefined, 'normal');
            doc.setFontSize(9);
            doc.text(row[3], 170, yPos + 3); // Évaluation
            doc.setFontSize(11);

            yPos += 10;
        });
    }

    yPos += 15;

    // === SECTION MÉTRIQUES DE PERFORMANCE ===
    doc.setFillColor(168, 85, 247);
    doc.rect(15, yPos - 5, 180, 12, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('METRIQUES DE PERFORMANCE', 20, yPos + 3);

    yPos += 25;

    // En-têtes du tableau de performance
    doc.setFillColor(168, 85, 247);
    doc.rect(15, yPos - 3, 180, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('METRIQUE', 20, yPos + 3);
    doc.text('VALEUR', 100, yPos + 3);
    doc.text('EVALUATION', 160, yPos + 3);

    yPos += 12;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Récupération des métriques avec valeurs par défaut
    const conversionRate = {{ $performanceMetrics['conversionRate'] ?? 0 }};
    const averageOrderValue = {{ $performanceMetrics['averageOrderValue'] ?? 0 }};
    const activeCustomers = {{ $performanceMetrics['activeCustomers'] ?? 0 }};
    const salesGrowth = {{ $performanceMetrics['salesGrowth'] ?? 0 }};

    const performanceData = [
        ['Taux de Conversion', conversionRate.toFixed(2) + '%', conversionRate > 70 ? 'EXCELLENT' : conversionRate > 40 ? 'BON' : 'FAIBLE'],
        ['Valeur Moy. Commande', '{{ number_format($performanceMetrics['averageOrderValue'] ?? 0, 0, ',', ' ') }} FCFA', averageOrderValue > 50000 ? 'ELEVEE' : 'STANDARD'],
        ['Clients Actifs', activeCustomers.toString(), activeCustomers > 50 ? 'NOMBREUX' : activeCustomers > 10 ? 'MOYEN' : 'PEU'],
        ['Croissance des Ventes', salesGrowth.toFixed(2) + '%', salesGrowth > 0 ? 'POSITIVE' : salesGrowth === 0 ? 'STABLE' : 'NEGATIVE']
    ];

    performanceData.forEach((row, index) => {
        // Couleurs alternées
        const bgColor = index % 2 === 0 ? [248, 250, 252] : [255, 255, 255];
        doc.setFillColor(...bgColor);
        doc.rect(15, yPos - 3, 180, 10, 'F');

        doc.setFontSize(11);
        doc.text(row[0], 20, yPos + 3); // Métrique
        doc.setFont(undefined, 'bold');
        doc.text(row[1], 100, yPos + 3); // Valeur
        doc.setFont(undefined, 'normal');
        doc.setFontSize(9);
        doc.text(row[2], 160, yPos + 3); // Évaluation
        doc.setFontSize(11);

        yPos += 10;
    });

    yPos += 15;

    // === RESUME FINAL ===
    doc.setFillColor(245, 245, 245);
    doc.roundedRect(15, yPos - 5, 180, 25, 5, 5, 'F');

    doc.setFontSize(12);
    doc.setFont(undefined, 'bold');
    doc.text('RESUME EXECUTIF', 20, yPos + 8);

    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    doc.text('Paiements recus: {{ number_format($totalPayments ?? 0, 0, ',', ' ') }} FCFA', 25, yPos + 16);
    doc.text('Taux de recouvrement: ' + (totalRevenue > 0 ? ((totalPayments / totalRevenue) * 100).toFixed(1) : 0) + '%', 120, yPos + 16);

    // === PIED DE PAGE ===
    doc.setFillColor(55, 65, 81);
    doc.rect(0, 275, 210, 22, 'F');

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(10);
    doc.text('(c) 2024 GRADIS - Systeme de Gestion Commerciale', 20, 285);
    doc.text('Page 1/1 | Confidentiel | ' + new Date().toLocaleDateString('fr-FR'), 120, 285);

    // Télécharger avec nom descriptif
    const fileName = 'GRADIS_Paiements_' + new Date().toLocaleDateString('fr-FR').replace(/\//g, '-') + '.pdf';
    doc.save(fileName);

    // Notification de succès
    showNotification('Export PDF Reussi', 'Rapport des paiements genere: ' + fileName, 'success');
    console.log('Export PDF des paiements professionnel termine');
}

function exportDashboardPDF() {
    console.log('📋 Démarrage export PDF du dashboard avec design amélioré...');

    // Debug des variables de factures
    console.log('🔍 Variables de factures:', {
        totalInvoices: {{ $totalInvoices ?? 0 }},
        paidInvoices: {{ $paidInvoices ?? 0 }},
        partialInvoices: {{ $partialInvoices ?? 0 }},
        unpaidInvoices: {{ $unpaidInvoices ?? 0 }}
    });

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // === PAGE DE COUVERTURE AMÉLIORÉE ===
    // Fond dégradé simulé
    doc.setFillColor(15, 23, 42);
    doc.rect(0, 0, 210, 297, 'F');

    // Logo/Titre principal
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(32);
    doc.setFont(undefined, 'bold');
    doc.text('GRADIS', 105, 80, { align: 'center' });

    doc.setFontSize(18);
    doc.setFont(undefined, 'normal');
    doc.text('DASHBOARD EXECUTIF', 105, 100, { align: 'center' });

    doc.setFontSize(14);
    doc.text('Rapport Complet de Performance', 105, 115, { align: 'center' });

    // Informations de génération dans un cadre
    doc.setFillColor(37, 99, 235);
    doc.roundedRect(40, 140, 130, 40, 5, 5, 'F');

    doc.setFontSize(12);
    doc.text('Genere le: ' + new Date().toLocaleDateString('fr-FR'), 105, 155, { align: 'center' });
    doc.text('Par: {{ auth()->user()->name }}', 105, 165, { align: 'center' });
    doc.text('Departement: Comptabilite', 105, 175, { align: 'center' });

    // Métriques clés en bas
    doc.setFillColor(59, 130, 246);
    doc.roundedRect(30, 220, 150, 50, 8, 8, 'F');

    doc.setFontSize(10);
    doc.text('APERCU RAPIDE', 105, 235, { align: 'center' });

    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('{{ number_format($totalRevenue ?? 0, 0, ',', ' ') }} FCFA', 105, 250, { align: 'center' });
    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    doc.text('Chiffre d\'Affaires Total', 105, 260, { align: 'center' });

    // === NOUVELLE PAGE - CONTENU PRINCIPAL ===
    doc.addPage();

    // En-tête de page
    doc.setFillColor(37, 99, 235);
    doc.rect(0, 0, 210, 25, 'F');

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(16);
    doc.setFont(undefined, 'bold');
    doc.text('TABLEAU DE BORD EXECUTIF', 20, 15);

    let yPos = 40;
    doc.setTextColor(0, 0, 0);

    // === SECTION KPI PRINCIPAUX ===
    doc.setFillColor(239, 246, 255);
    doc.roundedRect(15, yPos - 5, 180, 90, 5, 5, 'F');

    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('INDICATEURS CLES DE PERFORMANCE', 20, yPos + 8);

    yPos += 25;
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Tableau structuré des KPI
    const kpiData = [
        ['Chiffre d\'Affaires:', '{{ number_format($totalRevenue ?? 0, 0, ',', ' ') }} FCFA'],
        ['Nombre de Ventes:', '{{ $totalSales ?? 0 }}'],
        ['Paiements Recus:', '{{ number_format($totalPayments ?? 0, 0, ',', ' ') }} FCFA'],
        ['Paiements en Attente:', '{{ number_format($pendingPayments ?? 0, 0, ',', ' ') }} FCFA'],
        ['Clients Actifs:', '{{ $performanceMetrics['activeCustomers'] ?? 0 }}'],
        ['Taux de Conversion:', '{{ number_format($performanceMetrics['conversionRate'] ?? 0, 2) }}%']
    ];

    // En-têtes du tableau
    doc.setFillColor(59, 130, 246);
    doc.rect(20, yPos - 3, 170, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('INDICATEUR', 25, yPos + 3);
    doc.text('VALEUR', 130, yPos + 3);

    yPos += 12;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    kpiData.forEach((kpi, index) => {
        // Alternance de couleurs pour les lignes
        if (index % 2 === 0) {
            doc.setFillColor(248, 250, 252);
            doc.rect(20, yPos - 3, 170, 10, 'F');
        }

        doc.text(kpi[0], 25, yPos + 3);
        doc.setFont(undefined, 'bold');
        doc.text(kpi[1], 130, yPos + 3);
        doc.setFont(undefined, 'normal');

        yPos += 10;
    });

    yPos += 15;

    // === SECTION ANALYSE FINANCIÈRE ===
    doc.setFillColor(254, 249, 195);
    doc.roundedRect(15, yPos - 5, 180, 70, 5, 5, 'F');

    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('ANALYSE FINANCIERE', 20, yPos + 8);

    yPos += 25;

    // En-têtes du tableau financier
    doc.setFillColor(251, 191, 36);
    doc.rect(20, yPos - 3, 170, 10, 'F');
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('METRIQUE', 25, yPos + 3);
    doc.text('VALEUR', 90, yPos + 3);
    doc.text('EVALUATION', 140, yPos + 3);

    yPos += 12;
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    const financialMetrics = [
        ['Taux de Recouvrement:', '{{ $totalRevenue > 0 ? number_format(($totalPayments / $totalRevenue) * 100, 1) : 0 }}%', '{{ ($totalRevenue > 0 && ($totalPayments / $totalRevenue) > 0.8) ? "Excellent" : "A surveiller" }}'],
        ['Valeur Moy. Commande:', '{{ number_format($performanceMetrics['averageOrderValue'] ?? 0, 0, ',', ' ') }}', '{{ ($performanceMetrics['averageOrderValue'] ?? 0) > 50000 ? "Elevee" : "Standard" }}'],
        ['Croissance des Ventes:', '{{ number_format($performanceMetrics['salesGrowth'] ?? 0, 2) }}%', '{{ ($performanceMetrics['salesGrowth'] ?? 0) > 0 ? "Positive" : "Negative" }}']
    ];

    financialMetrics.forEach((metric, index) => {
        // Alternance de couleurs
        if (index % 2 === 0) {
            doc.setFillColor(255, 253, 235);
            doc.rect(20, yPos - 3, 170, 10, 'F');
        }

        doc.text(metric[0], 25, yPos + 3);
        doc.setFont(undefined, 'bold');
        doc.text(metric[1], 90, yPos + 3);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(9);
        doc.text(metric[2], 140, yPos + 3);
        doc.setFontSize(11);

        yPos += 10;
    });

    yPos += 15;

    // === SECTION STATUT DES FACTURES ===
    doc.setFillColor(240, 253, 244);
    doc.roundedRect(15, yPos - 5, 180, 80, 5, 5, 'F');

    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('STATUT DES FACTURES', 20, yPos + 8);

    yPos += 25;

    // En-têtes du tableau des factures
    doc.setFillColor(34, 197, 94);
    doc.rect(20, yPos - 3, 170, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('TYPE', 25, yPos + 3);
    doc.text('NOMBRE', 80, yPos + 3);
    doc.text('POURCENTAGE', 120, yPos + 3);
    doc.text('STATUT', 160, yPos + 3);

    yPos += 12;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Calculs des pourcentages avec protection contre division par zéro
    const totalInvoices = {{ $totalInvoices ?? 0 }};
    const paidInvoices = {{ $paidInvoices ?? 0 }};
    const partialInvoices = {{ $partialInvoices ?? 0 }};
    const unpaidInvoices = {{ $unpaidInvoices ?? 0 }};

    // Calculer les pourcentages seulement si totalInvoices > 0
    let paidPercent = 0;
    let partialPercent = 0;
    let unpaidPercent = 0;

    if (totalInvoices > 0) {
        paidPercent = Math.round((paidInvoices / totalInvoices) * 100);
        partialPercent = Math.round((partialInvoices / totalInvoices) * 100);
        unpaidPercent = Math.round((unpaidInvoices / totalInvoices) * 100);
    }

    const invoiceData = [
        ['Factures Payees', paidInvoices.toString(), paidPercent + '%', 'OK'],
        ['Factures Partielles', partialInvoices.toString(), partialPercent + '%', 'SUIVI'],
        ['Factures Impayees', unpaidInvoices.toString(), unpaidPercent + '%', 'URGENT'],
        ['TOTAL', totalInvoices.toString(), totalInvoices > 0 ? '100%' : '0%', '---']
    ];

    // Vérifier s'il y a des données à afficher
    if (totalInvoices === 0) {
        // Afficher un message si aucune facture
        doc.setFillColor(255, 243, 205);
        doc.rect(20, yPos - 3, 170, 15, 'F');
        doc.setFontSize(11);
        doc.setFont(undefined, 'italic');
        doc.text('Aucune facture trouvee dans le systeme', 25, yPos + 5);
        doc.setFont(undefined, 'normal');
        yPos += 15;
    } else {
        // Afficher les données des factures
        invoiceData.forEach((invoice, index) => {
            // Couleur de fond selon le type
            if (index === 0) doc.setFillColor(220, 252, 231); // Vert clair
            else if (index === 1) doc.setFillColor(254, 249, 195); // Jaune clair
            else if (index === 2) doc.setFillColor(254, 226, 226); // Rouge clair
            else doc.setFillColor(243, 244, 246); // Gris clair

            doc.rect(20, yPos - 3, 170, 10, 'F');

            doc.text(invoice[0], 25, yPos + 3);
            doc.setFont(undefined, 'bold');
            doc.text(invoice[1], 80, yPos + 3);
            doc.text(invoice[2], 120, yPos + 3);
            doc.setFont(undefined, 'normal');
            doc.setFontSize(9);
            doc.text(invoice[3], 160, yPos + 3);
            doc.setFontSize(11);

            yPos += 10;
        });
    }

    yPos += 10;

    // === RESUME FINAL ===
    doc.setFillColor(245, 245, 245);
    doc.roundedRect(15, yPos - 5, 180, 30, 5, 5, 'F');

    doc.setFontSize(12);
    doc.setFont(undefined, 'bold');
    doc.text('RESUME EXECUTIF', 20, yPos + 8);

    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    doc.text('Chiffre d\'affaires: {{ number_format($totalRevenue ?? 0, 0, ',', ' ') }} FCFA', 25, yPos + 18);
    doc.text('Taux de recouvrement: {{ $totalRevenue > 0 ? number_format(($totalPayments / $totalRevenue) * 100, 1) : 0 }}%', 25, yPos + 25);

    // === PIED DE PAGE PROFESSIONNEL ===
    doc.setFillColor(15, 23, 42);
    doc.rect(0, 270, 210, 27, 'F');

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(10);
    doc.text('GRADIS - Systeme de Gestion Commerciale Avance', 20, 285);
    doc.text('Rapport confidentiel | Page 1/1 | ' + new Date().toLocaleDateString('fr-FR'), 20, 292);

    // Télécharger avec nom descriptif
    const fileName = 'GRADIS_Dashboard_Executif_' + new Date().toLocaleDateString('fr-FR').replace(/\//g, '-') + '.pdf';
    doc.save(fileName);

    // Notification de succès
    showNotification('Export Dashboard Reussi', 'Rapport executif genere: ' + fileName, 'success');
    console.log('Export Dashboard PDF executif termine');
}

function openCustomReport() {
    console.log('⚙️ Ouverture du configurateur de rapport...');

    // Créer une modal pour le rapport personnalisé
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Rapport Personnalisé</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="customReportForm">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Période d'analyse</label>
                                <select class="form-select" name="dateRange" required>
                                    <option value="week">Cette semaine (7 jours)</option>
                                    <option value="month" selected>Ce mois (30 jours)</option>
                                    <option value="quarter">Ce trimestre (3 mois)</option>
                                    <option value="year">Cette année (12 mois)</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Format de sortie</label>
                                <select class="form-select" name="reportType" required>
                                    <option value="pdf" selected>PDF (Portable)</option>
                                    <option value="excel">Excel (Analyse)</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-4">
                            <label class="form-label">Options d'inclusion</label>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeCharts" id="includeCharts" checked>
                                        <label class="form-check-label" for="includeCharts">
                                            <i class="fas fa-chart-bar text-primary"></i> Graphiques
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeDetails" id="includeDetails" checked>
                                        <label class="form-check-label" for="includeDetails">
                                            <i class="fas fa-list-ul text-success"></i> Détails
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" name="includeRecommendations" id="includeRecommendations">
                                        <label class="form-check-label" for="includeRecommendations">
                                            <i class="fas fa-lightbulb text-warning"></i> Recommandations
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong>Aperçu :</strong> Le rapport sera généré avec les données ajustées selon la période sélectionnée.
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times"></i> Annuler
                    </button>
                    <button type="button" class="btn btn-primary" id="generateReportBtn" onclick="generateCustomReport()">
                        <i class="fas fa-download"></i> Générer le Rapport
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // Ajouter un gestionnaire d'événement alternatif pour le bouton
    const generateBtn = modal.querySelector('#generateReportBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            console.log('🔘 Clic sur le bouton Générer détecté');
            generateCustomReport();
        });
    }

    // Nettoyer après fermeture
    modal.addEventListener('hidden.bs.modal', () => {
        document.body.removeChild(modal);
    });
}

function generateCustomReport() {
    console.log('🚀 Démarrage génération du rapport personnalisé...');

    try {
        // Désactiver le bouton pendant la génération
        const generateBtn = document.getElementById('generateReportBtn');
        if (generateBtn) {
            generateBtn.disabled = true;
            generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Génération...';
        }

        // Récupérer les données du formulaire
        const form = document.getElementById('customReportForm');
        if (!form) {
            throw new Error('Formulaire non trouvé');
        }

        const formData = new FormData(form);

        const reportType = formData.get('reportType');
        const dateRange = formData.get('dateRange');
        const includeCharts = formData.has('includeCharts');
        const includeDetails = formData.has('includeDetails');
        const includeRecommendations = formData.has('includeRecommendations');

        console.log('📋 Configuration du rapport:', {
            type: reportType,
            dateRange: dateRange,
            charts: includeCharts,
            details: includeDetails,
            recommendations: includeRecommendations
        });

        // Validation des données
        if (!reportType || !dateRange) {
            throw new Error('Veuillez sélectionner un type de rapport et une période');
        }

        // Générer le rapport selon le type choisi
        setTimeout(() => {
            try {
                if (reportType === 'excel') {
                    console.log('📊 Génération Excel...');
                    generateCustomExcelReport(dateRange, includeCharts, includeDetails, includeRecommendations);
                } else if (reportType === 'pdf') {
                    console.log('📄 Génération PDF...');
                    generateCustomPDFReport(dateRange, includeCharts, includeDetails, includeRecommendations);
                } else {
                    throw new Error('Type de rapport non reconnu: ' + reportType);
                }

                // Fermer la modal après succès
                const modal = document.querySelector('.modal.show');
                if (modal) {
                    bootstrap.Modal.getInstance(modal).hide();
                }

            } catch (error) {
                console.error('❌ Erreur lors de la génération:', error);
                showNotification('Erreur de Génération', error.message, 'error');

                // Réactiver le bouton
                if (generateBtn) {
                    generateBtn.disabled = false;
                    generateBtn.innerHTML = '<i class="fas fa-download"></i> Générer le Rapport';
                }
            }
        }, 100); // Petit délai pour permettre l'affichage du spinner

    } catch (error) {
        console.error('❌ Erreur dans generateCustomReport:', error);
        showNotification('Erreur', error.message, 'error');

        // Réactiver le bouton
        const generateBtn = document.getElementById('generateReportBtn');
        if (generateBtn) {
            generateBtn.disabled = false;
            generateBtn.innerHTML = '<i class="fas fa-download"></i> Générer le Rapport';
        }
    }
}

function generateCustomExcelReport(dateRange, includeCharts, includeDetails, includeRecommendations) {
    console.log('Generation du rapport Excel personnalise...');

    // Créer un nouveau workbook
    const wb = XLSX.utils.book_new();

    // === FEUILLE PRINCIPALE: RAPPORT PERSONNALISE ===
    const customData = [
        ['RAPPORT PERSONNALISE - GRADIS', '', '', ''],
        ['Genere le:', new Date().toLocaleDateString('fr-FR'), 'Periode:', dateRange],
        ['Par:', '{{ auth()->user()->name }}', 'Role:', 'Comptable'],
        ['', '', '', ''],
        ['CONFIGURATION DU RAPPORT', '', '', ''],
        ['Graphiques inclus:', includeCharts ? 'OUI' : 'NON', 'Details inclus:', includeDetails ? 'OUI' : 'NON'],
        ['Recommandations:', includeRecommendations ? 'OUI' : 'NON', '', ''],
        ['', '', '', ''],
        ['RESUME FINANCIER PERSONNALISE', '', '', ''],
        ['Indicateur', 'Valeur', 'Unite', 'Evaluation']
    ];

    // Ajouter les données selon la période
    const currentDate = new Date();
    let periodMultiplier = 1;

    switch(dateRange) {
        case 'week':
            periodMultiplier = 0.25;
            customData.push(['Periode analysee', '7 derniers jours', '', 'COURT TERME']);
            break;
        case 'month':
            periodMultiplier = 1;
            customData.push(['Periode analysee', '30 derniers jours', '', 'MOYEN TERME']);
            break;
        case 'quarter':
            periodMultiplier = 3;
            customData.push(['Periode analysee', '3 derniers mois', '', 'LONG TERME']);
            break;
        case 'year':
            periodMultiplier = 12;
            customData.push(['Periode analysee', '12 derniers mois', '', 'TRES LONG TERME']);
            break;
    }

    // Données financières ajustées selon la période
    const adjustedRevenue = Math.round(({{ $totalRevenue ?? 0 }}) * periodMultiplier);
    const adjustedSales = Math.round(({{ $totalSales ?? 0 }}) * periodMultiplier);
    const adjustedPayments = Math.round(({{ $totalPayments ?? 0 }}) * periodMultiplier);

    customData.push(
        ['Chiffre d\'affaires', adjustedRevenue, 'FCFA', adjustedRevenue > 100000 ? 'EXCELLENT' : 'MOYEN'],
        ['Nombre de ventes', adjustedSales, 'Ventes', adjustedSales > 10 ? 'ACTIF' : 'FAIBLE'],
        ['Paiements recus', adjustedPayments, 'FCFA', adjustedPayments > 50000 ? 'BON' : 'A AMELIORER'],
        ['', '', '', '']
    );

    // Ajouter les détails si demandés
    if (includeDetails) {
        customData.push(
            ['DETAILS COMPLEMENTAIRES', '', '', ''],
            ['Taux de conversion', '{{ number_format($performanceMetrics['conversionRate'] ?? 0, 2) }}', '%', '{{ ($performanceMetrics['conversionRate'] ?? 0) > 70 ? "EXCELLENT" : "MOYEN" }}'],
            ['Clients actifs', '{{ $performanceMetrics['activeCustomers'] ?? 0 }}', 'Clients', '{{ ($performanceMetrics['activeCustomers'] ?? 0) > 20 ? "NOMBREUX" : "LIMITE" }}'],
            ['Valeur moy. commande', '{{ number_format($performanceMetrics['averageOrderValue'] ?? 0, 0, ',', ' ') }}', 'FCFA', '{{ ($performanceMetrics['averageOrderValue'] ?? 0) > 30000 ? "ELEVEE" : "STANDARD" }}'],
            ['', '', '', '']
        );
    }

    // Ajouter les recommandations si demandées
    if (includeRecommendations) {
        customData.push(
            ['RECOMMANDATIONS PERSONNALISEES', '', '', ''],
            ['Action prioritaire', 'Augmenter les ventes', 'Priorite', 'HAUTE'],
            ['Suivi requis', 'Relancer les paiements', 'Priorite', 'MOYENNE'],
            ['Amelioration', 'Optimiser la conversion', 'Priorite', 'CONTINUE']
        );
    }

    const ws1 = XLSX.utils.aoa_to_sheet(customData);

    // Styles pour le rapport personnalisé
    ws1['!cols'] = [
        { width: 25 }, { width: 20 }, { width: 15 }, { width: 15 }
    ];

    // Styles avancés
    if (!ws1['!merges']) ws1['!merges'] = [];
    if (!ws1['!rows']) ws1['!rows'] = [];

    // Titre principal
    ws1['!merges'].push({ s: { r: 0, c: 0 }, e: { r: 0, c: 3 } });
    ws1['!rows'][0] = { hpt: 30 };

    if (ws1['A1']) {
        ws1['A1'].s = {
            font: { bold: true, sz: 16, color: { rgb: "FFFFFF" } },
            fill: { fgColor: { rgb: "8B5CF6" } },
            alignment: { horizontal: "center", vertical: "center" },
            border: {
                top: { style: "thick", color: { rgb: "000000" } },
                bottom: { style: "thick", color: { rgb: "000000" } },
                left: { style: "thick", color: { rgb: "000000" } },
                right: { style: "thick", color: { rgb: "000000" } }
            }
        };
    }

    // Styliser les sections
    const cellRefs = Object.keys(ws1).filter(key => key.match(/^[A-Z]+[0-9]+$/));
    cellRefs.forEach(ref => {
        const cell = ws1[ref];
        if (cell && cell.v) {
            const row = parseInt(ref.match(/[0-9]+/)[0]);
            const value = cell.v.toString();

            if (value.includes('CONFIGURATION') || value.includes('RESUME') || value.includes('DETAILS') || value.includes('RECOMMANDATIONS')) {
                cell.s = {
                    font: { bold: true, sz: 12, color: { rgb: "FFFFFF" } },
                    fill: { fgColor: { rgb: "8B5CF6" } },
                    alignment: { horizontal: "center" },
                    border: {
                        top: { style: "medium", color: { rgb: "000000" } },
                        bottom: { style: "medium", color: { rgb: "000000" } },
                        left: { style: "medium", color: { rgb: "000000" } },
                        right: { style: "medium", color: { rgb: "000000" } }
                    }
                };
                ws1['!merges'].push({ s: { r: row-1, c: 0 }, e: { r: row-1, c: 3 } });
            } else if (value === 'Indicateur' || value === 'Action prioritaire') {
                cell.s = {
                    font: { bold: true, sz: 11, color: { rgb: "000000" } },
                    fill: { fgColor: { rgb: "E5E7EB" } },
                    alignment: { horizontal: "center" },
                    border: {
                        top: { style: "medium", color: { rgb: "000000" } },
                        bottom: { style: "medium", color: { rgb: "000000" } },
                        left: { style: "thin", color: { rgb: "000000" } },
                        right: { style: "thin", color: { rgb: "000000" } }
                    }
                };
            } else if (row > 1 && value !== '') {
                const isEvenRow = row % 2 === 0;
                cell.s = {
                    font: { sz: 10 },
                    fill: { fgColor: { rgb: isEvenRow ? "F3F4F6" : "FFFFFF" } },
                    alignment: { horizontal: "left" },
                    border: {
                        top: { style: "thin", color: { rgb: "D1D5DB" } },
                        bottom: { style: "thin", color: { rgb: "D1D5DB" } },
                        left: { style: "thin", color: { rgb: "D1D5DB" } },
                        right: { style: "thin", color: { rgb: "D1D5DB" } }
                    }
                };
            }
        }
    });

    // Ajouter la feuille au workbook
    XLSX.utils.book_append_sheet(wb, ws1, "Rapport Personnalise");

    // Télécharger le fichier
    const fileName = 'GRADIS_Rapport_Personnalise_' + dateRange + '_' + new Date().toLocaleDateString('fr-FR').replace(/\//g, '-') + '.xlsx';
    XLSX.writeFile(wb, fileName);

    // Notification de succès
    showNotification('Export Excel Reussi', 'Rapport personnalise genere: ' + fileName, 'success');
    console.log('Export Excel personnalise termine');
}

function generateCustomPDFReport(dateRange, includeCharts, includeDetails, includeRecommendations) {
    console.log('Generation du rapport PDF personnalise...');

    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();

    // === EN-TÊTE PERSONNALISÉ ===
    doc.setFillColor(139, 92, 246); // Violet pour rapport personnalisé
    doc.rect(0, 0, 210, 40, 'F');

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(24);
    doc.setFont(undefined, 'bold');
    doc.text('GRADIS', 20, 20);

    doc.setFontSize(16);
    doc.text('RAPPORT PERSONNALISE', 20, 30);

    // Informations de génération
    doc.setTextColor(0, 0, 0);
    doc.setFontSize(10);
    doc.text('Genere le: ' + new Date().toLocaleDateString('fr-FR') + ' a ' + new Date().toLocaleTimeString('fr-FR'), 20, 50);
    doc.text('Par: {{ auth()->user()->name }} | Periode: ' + dateRange, 20, 57);

    let yPos = 75;

    // === CONFIGURATION DU RAPPORT ===
    doc.setFillColor(139, 92, 246);
    doc.rect(15, yPos - 5, 180, 12, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('CONFIGURATION DU RAPPORT', 20, yPos + 3);

    yPos += 20;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Tableau de configuration
    const configData = [
        ['Graphiques inclus:', includeCharts ? 'OUI' : 'NON'],
        ['Details inclus:', includeDetails ? 'OUI' : 'NON'],
        ['Recommandations:', includeRecommendations ? 'OUI' : 'NON']
    ];

    configData.forEach((row, index) => {
        const bgColor = index % 2 === 0 ? [248, 250, 252] : [255, 255, 255];
        doc.setFillColor(...bgColor);
        doc.rect(15, yPos - 3, 180, 10, 'F');

        doc.text(row[0], 20, yPos + 3);
        doc.setFont(undefined, 'bold');
        doc.text(row[1], 120, yPos + 3);
        doc.setFont(undefined, 'normal');

        yPos += 10;
    });

    yPos += 15;

    // === DONNEES FINANCIERES PERSONNALISEES ===
    doc.setFillColor(139, 92, 246);
    doc.rect(15, yPos - 5, 180, 12, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    doc.text('DONNEES FINANCIERES - ' + dateRange.toUpperCase(), 20, yPos + 3);

    yPos += 25;

    // En-têtes du tableau financier
    doc.setFillColor(139, 92, 246);
    doc.rect(15, yPos - 3, 180, 10, 'F');
    doc.setTextColor(255, 255, 255);
    doc.setFont(undefined, 'bold');
    doc.setFontSize(10);
    doc.text('INDICATEUR', 20, yPos + 3);
    doc.text('VALEUR', 100, yPos + 3);
    doc.text('EVALUATION', 150, yPos + 3);

    yPos += 12;
    doc.setTextColor(0, 0, 0);
    doc.setFont(undefined, 'normal');
    doc.setFontSize(11);

    // Calculer les données selon la période
    let periodMultiplier = 1;
    let periodLabel = '';

    switch(dateRange) {
        case 'week':
            periodMultiplier = 0.25;
            periodLabel = '7 jours';
            break;
        case 'month':
            periodMultiplier = 1;
            periodLabel = '30 jours';
            break;
        case 'quarter':
            periodMultiplier = 3;
            periodLabel = '3 mois';
            break;
        case 'year':
            periodMultiplier = 12;
            periodLabel = '12 mois';
            break;
    }

    const adjustedRevenue = Math.round(({{ $totalRevenue ?? 0 }}) * periodMultiplier);
    const adjustedSales = Math.round(({{ $totalSales ?? 0 }}) * periodMultiplier);
    const adjustedPayments = Math.round(({{ $totalPayments ?? 0 }}) * periodMultiplier);

    const financialData = [
        ['Periode analysee', periodLabel, 'PERSONNALISE'],
        ['Chiffre d\'affaires', adjustedRevenue.toLocaleString('fr-FR') + ' FCFA', adjustedRevenue > 100000 ? 'EXCELLENT' : 'MOYEN'],
        ['Nombre de ventes', adjustedSales.toString(), adjustedSales > 10 ? 'ACTIF' : 'FAIBLE'],
        ['Paiements recus', adjustedPayments.toLocaleString('fr-FR') + ' FCFA', adjustedPayments > 50000 ? 'BON' : 'A AMELIORER']
    ];

    financialData.forEach((row, index) => {
        const bgColor = index % 2 === 0 ? [248, 250, 252] : [255, 255, 255];
        doc.setFillColor(...bgColor);
        doc.rect(15, yPos - 3, 180, 10, 'F');

        doc.text(row[0], 20, yPos + 3);
        doc.setFont(undefined, 'bold');
        doc.text(row[1], 100, yPos + 3);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(9);
        doc.text(row[2], 150, yPos + 3);
        doc.setFontSize(11);

        yPos += 10;
    });

    yPos += 15;

    // === DETAILS COMPLEMENTAIRES (si demandés) ===
    if (includeDetails) {
        doc.setFillColor(34, 197, 94);
        doc.rect(15, yPos - 5, 180, 12, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text('DETAILS COMPLEMENTAIRES', 20, yPos + 3);

        yPos += 20;
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(11);

        const detailsData = [
            ['Taux de conversion', '{{ number_format($performanceMetrics['conversionRate'] ?? 0, 2) }}%', '{{ ($performanceMetrics['conversionRate'] ?? 0) > 70 ? "EXCELLENT" : "MOYEN" }}'],
            ['Clients actifs', '{{ $performanceMetrics['activeCustomers'] ?? 0 }}', '{{ ($performanceMetrics['activeCustomers'] ?? 0) > 20 ? "NOMBREUX" : "LIMITE" }}'],
            ['Valeur moy. commande', '{{ number_format($performanceMetrics['averageOrderValue'] ?? 0, 0, ',', ' ') }} FCFA', '{{ ($performanceMetrics['averageOrderValue'] ?? 0) > 30000 ? "ELEVEE" : "STANDARD" }}']
        ];

        detailsData.forEach((row, index) => {
            const bgColor = index % 2 === 0 ? [220, 252, 231] : [255, 255, 255];
            doc.setFillColor(...bgColor);
            doc.rect(15, yPos - 3, 180, 10, 'F');

            doc.text(row[0], 20, yPos + 3);
            doc.setFont(undefined, 'bold');
            doc.text(row[1], 100, yPos + 3);
            doc.setFont(undefined, 'normal');
            doc.setFontSize(9);
            doc.text(row[2], 150, yPos + 3);
            doc.setFontSize(11);

            yPos += 10;
        });

        yPos += 15;
    }

    // === RECOMMANDATIONS (si demandées) ===
    if (includeRecommendations) {
        doc.setFillColor(239, 68, 68);
        doc.rect(15, yPos - 5, 180, 12, 'F');
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(14);
        doc.setFont(undefined, 'bold');
        doc.text('RECOMMANDATIONS PERSONNALISEES', 20, yPos + 3);

        yPos += 20;
        doc.setTextColor(0, 0, 0);
        doc.setFont(undefined, 'normal');
        doc.setFontSize(11);

        const recommendationsData = [
            ['Action prioritaire', 'Augmenter les ventes', 'HAUTE'],
            ['Suivi requis', 'Relancer les paiements', 'MOYENNE'],
            ['Amelioration', 'Optimiser la conversion', 'CONTINUE']
        ];

        recommendationsData.forEach((row, index) => {
            const bgColor = index % 2 === 0 ? [254, 226, 226] : [255, 255, 255];
            doc.setFillColor(...bgColor);
            doc.rect(15, yPos - 3, 180, 10, 'F');

            doc.text(row[0], 20, yPos + 3);
            doc.setFont(undefined, 'bold');
            doc.text(row[1], 100, yPos + 3);
            doc.setFont(undefined, 'normal');
            doc.setFontSize(9);
            doc.text(row[2], 150, yPos + 3);
            doc.setFontSize(11);

            yPos += 10;
        });
    }

    // === PIED DE PAGE ===
    doc.setFillColor(55, 65, 81);
    doc.rect(0, 275, 210, 22, 'F');

    doc.setTextColor(255, 255, 255);
    doc.setFontSize(10);
    doc.text('(c) 2024 GRADIS - Rapport Personnalise', 20, 285);
    doc.text('Page 1/1 | Confidentiel | ' + new Date().toLocaleDateString('fr-FR'), 120, 285);

    // Télécharger le fichier
    const fileName = 'GRADIS_Rapport_Personnalise_' + dateRange + '_' + new Date().toLocaleDateString('fr-FR').replace(/\//g, '-') + '.pdf';
    doc.save(fileName);

    // Notification de succès
    showNotification('Export PDF Reussi', 'Rapport personnalise genere: ' + fileName, 'success');
    console.log('Export PDF personnalise termine');
}

// Fonction de notification élégante
function showNotification(title, message, type = 'info') {
    // Créer l'élément de notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'success' ? 'success' : type === 'error' ? 'danger' : 'info'} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border: none;
        border-radius: 10px;
    `;

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <div class="me-3">
                ${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}
            </div>
            <div class="flex-grow-1">
                <strong>${title}</strong><br>
                <small>${message}</small>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // Auto-suppression après 5 secondes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Fonction de test globale


document.addEventListener('DOMContentLoaded', function() {
    console.log('🔍 DIAGNOSTIC IMMÉDIAT');

    // Debug des variables de factures au chargement
    console.log('🔍 Variables de factures au chargement:', {
        totalInvoices: {{ $totalInvoices ?? 'undefined' }},
        paidInvoices: {{ $paidInvoices ?? 'undefined' }},
        partialInvoices: {{ $partialInvoices ?? 'undefined' }},
        unpaidInvoices: {{ $unpaidInvoices ?? 'undefined' }}
    });

    // Vérifier si les fonctions sont disponibles
    setTimeout(function() {
        console.log('📊 État des fonctions après chargement:', {
            initAdvancedDashboard: typeof window.initAdvancedDashboard,
            exportSalesExcel: typeof window.exportSalesExcel,
            handleExport: typeof window.handleExport
        });

        // Vérifier les boutons
        const exportBtns = document.querySelectorAll('.export-btn');
        console.log('🎯 Boutons d\'export trouvés:', exportBtns.length);

        // Attacher manuellement les event listeners si nécessaire
        exportBtns.forEach((btn, index) => {
            console.log(`Bouton ${index + 1}:`, btn.dataset.export);

            // Supprimer les anciens listeners et en ajouter de nouveaux
            btn.replaceWith(btn.cloneNode(true));
            const newBtn = document.querySelectorAll('.export-btn')[index];

            newBtn.addEventListener('click', function(e) {
                e.preventDefault();
                console.log('🚀 Clic détecté sur:', this.dataset.export);

                // Ajouter l'animation de chargement
                this.classList.add('loading');
                const originalText = this.querySelector('span').textContent;
                this.querySelector('span').textContent = 'Génération...';
                this.querySelector('i').className = 'fas fa-spinner';

                // Délai pour l'animation
                setTimeout(() => {
                    try {
                        // Appeler les vraies fonctions d'export
                        switch(this.dataset.export) {
                            case 'sales-excel':
                                exportSalesExcel();
                                break;
                            case 'payments-pdf':
                                exportPaymentsPDF();
                                break;
                            case 'dashboard-pdf':
                                exportDashboardPDF();
                                break;
                            case 'custom-report':
                                openCustomReport();
                                break;
                            default:
                                console.log('❓ Type d\'export inconnu:', this.dataset.export);
                                showNotification('❌ Erreur', 'Type d\'export non reconnu: ' + this.dataset.export, 'error');
                        }
                    } catch (error) {
                        console.error('Erreur lors de l\'export:', error);
                        showNotification('❌ Erreur d\'Export', 'Une erreur est survenue lors de la génération du fichier.', 'error');
                    } finally {
                        // Restaurer l'état du bouton
                        this.classList.remove('loading');
                        this.querySelector('span').textContent = originalText;

                        // Restaurer l'icône originale
                        const iconMap = {
                            'sales-excel': 'fas fa-file-excel',
                            'payments-pdf': 'fas fa-file-pdf',
                            'dashboard-pdf': 'fas fa-chart-bar',
                            'custom-report': 'fas fa-cog'
                        };
                        this.querySelector('i').className = iconMap[this.dataset.export] || 'fas fa-download';
                    }
                }, 500); // Délai de 500ms pour l'animation
            });
        });



        console.log('✅ Event listeners attachés manuellement');

    }, 1000); // Attendre 1 seconde pour que tout soit chargé
});
</script>

<!-- Scripts de compatibilité supprimés - gérés dans dashboard-professional-optimized.js -->

<!-- Script de graphique supprimé - géré dans dashboard-professional-optimized.js -->

<!-- Script d'activités supprimé - géré dans dashboard-professional-optimized.js -->

    // ===== FONCTIONNALITÉS SECTION INFORMATIONS MODERNISÉE =====

    // Gestion des onglets
    const infoTabs = document.querySelectorAll('.info-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    infoTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Retirer la classe active de tous les onglets
            infoTabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Ajouter la classe active à l'onglet cliqué
            this.classList.add('active');

            // Afficher le contenu correspondant
            const targetTab = this.dataset.tab;
            const targetContent = document.getElementById(targetTab + '-tab');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // Animation d'entrée pour les widgets
            const widgets = targetContent.querySelectorAll('.modern-info-widget, .alert-item');
            widgets.forEach((widget, index) => {
                widget.style.opacity = '0';
                widget.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    widget.style.transition = 'all 0.4s ease';
                    widget.style.opacity = '1';
                    widget.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    });

    // Actualisation des données
    const refreshBtn = document.getElementById('refreshInfoData');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            this.style.animation = 'spin 1s linear';

            // Simuler le rechargement des données
            setTimeout(() => {
                // Mettre à jour les valeurs avec animation
                updateWidgetValues();
                this.style.animation = '';
            }, 1000);
        });
    }

    // Fonction pour mettre à jour les valeurs des widgets
    function updateWidgetValues() {
        const treasuryValue = document.getElementById('treasuryValue');
        const overdueCount = document.getElementById('overdueCount');

        if (treasuryValue) {
            animateValue(treasuryValue, 1250000, 1275000, 'F');
        }

        if (overdueCount) {
            animateValue(overdueCount, 8, 7, '');
        }
    }

    // Animation des valeurs numériques
    function animateValue(element, start, end, suffix = '') {
        const duration = 1000;
        const startTime = performance.now();

        function update(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const current = Math.floor(start + (end - start) * progress);
            element.textContent = new Intl.NumberFormat().format(current) + (suffix ? ' ' + suffix : '');

            if (progress < 1) {
                requestAnimationFrame(update);
            }
        }

        requestAnimationFrame(update);
    }

    // Initialisation du mini-graphique de trésorerie
    const treasuryChart = document.getElementById('treasuryMiniChart');
    if (treasuryChart) {
        const ctx = treasuryChart.getContext('2d');
        const gradient = ctx.createLinearGradient(0, 0, 0, 30);
        gradient.addColorStop(0, 'rgba(16, 185, 129, 0.8)');
        gradient.addColorStop(1, 'rgba(16, 185, 129, 0.2)');

        // Données simulées pour le graphique
        const data = [1200000, 1220000, 1180000, 1250000, 1275000];
        const max = Math.max(...data);
        const min = Math.min(...data);

        ctx.strokeStyle = '#10b981';
        ctx.fillStyle = gradient;
        ctx.lineWidth = 2;

        ctx.beginPath();
        data.forEach((value, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 30 - ((value - min) / (max - min)) * 25;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });

        ctx.lineTo(100, 30);
        ctx.lineTo(0, 30);
        ctx.closePath();
        ctx.fill();

        ctx.beginPath();
        data.forEach((value, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 30 - ((value - min) / (max - min)) * 25;

            if (index === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
        });
        ctx.stroke();
    }

    console.log('✅ Section informations modernisée initialisée');
</script>

<!-- Script de widgets supprimé - géré dans dashboard-professional-optimized.js -->

















<!-- Scripts de monitoring désactivés pour optimiser les performances -->
{{--
<script src="{{ asset('js/dashboard-performance-monitor.js') }}"></script>
@if(config('app.debug'))
<script src="{{ asset('js/dashboard-performance-test.js') }}"></script>
@endif
--}}

<!-- Script principal du tableau de bord (déjà inclus plus haut) -->


<!-- Scripts de performance optimisés et conditionnels -->
@if(config('app.debug') || request()->get('debug_performance'))
    <!-- Script de mesure de performance (seulement en mode debug) -->
    <script>
    // Chargement conditionnel du tracker de performance
    if (window.location.search.includes('debug_performance') || {{ config('app.debug') ? 'true' : 'false' }}) {
        loadScript('{{ asset('js/dashboard-performance-tracker.js') }}?v={{ time() }}')
            .then(() => console.log('📊 Tracker de performance chargé'))
            .catch(error => console.warn('⚠️ Impossible de charger le tracker de performance:', error));
    }
    </script>
@endif

<!-- Configuration d'optimisation ULTRA intégrée -->
<script>
// Configuration d'optimisation ULTRA pour améliorer les performances
window.DASHBOARD_OPTIMIZATION = {
    disableAnimations: true,        // Désactiver toutes les animations
    disableAutoRefresh: true,       // Désactiver l'auto-refresh
    reducedPolling: true,           // Réduire la fréquence des mises à jour
    lazyLoadCharts: true,           // Charger les graphiques seulement quand nécessaire
    cacheResults: true,             // Mettre en cache les résultats
    ultraMode: true,                // Mode ultra-optimisé
    disableNotifications: false,    // Garder les notifications importantes
    disableCounters: true,          // Désactiver les compteurs animés
    disableProgressBars: true,      // Désactiver les barres de progression animées
    disableTransitions: true        // Désactiver les transitions CSS
};

// Fonction d'optimisation des performances intégrée
function optimizeDashboardPerformance() {
    console.log('🚀 Optimisation ULTRA des performances...');

    // Réduire les animations si configuré
    if (window.DASHBOARD_OPTIMIZATION.disableAnimations) {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-delay: 0.01ms !important;
                transition-duration: 0.01ms !important;
                transition-delay: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }

    // Nettoyer les intervalles existants pour éviter les fuites mémoire
    ['countdownInterval', 'refreshInterval', 'sessionInterval'].forEach(intervalName => {
        if (typeof window[intervalName] !== 'undefined' && window[intervalName]) {
            clearInterval(window[intervalName]);
            window[intervalName] = null;
        }
    });

    console.log('✅ Optimisation ULTRA terminée');
}

// Appliquer l'optimisation immédiatement
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', optimizeDashboardPerformance);
} else {
    optimizeDashboardPerformance();
}

console.log('🔧 Optimisation des performances intégrée');
</script>

@if(config('app.debug'))
    <!-- Script d'optimisation avancé (seulement en mode debug) -->
    <script>
    // Chargement conditionnel de l'optimiseur avancé
    loadScript('{{ asset('js/dashboard-performance-optimizer.js') }}')
        .then(() => console.log('🔧 Optimiseur avancé chargé'))
        .catch(error => console.warn('⚠️ Optimiseur avancé non disponible:', error));

    // Chargement du testeur de performance en mode debug
    loadScript('{{ asset('js/performance-test.js') }}')
        .then(() => console.log('📊 Testeur de performance chargé'))
        .catch(error => console.warn('⚠️ Testeur de performance non disponible:', error));
    </script>
@endif

@endpush
