<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class OptimizeResponse
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Ne pas appliquer la compression GZIP manuelle en développement
        // Laisser le serveur web (Apache/Nginx) gérer la compression
        if (config('app.env') === 'production' &&
            function_exists('gzencode') &&
            !$response->headers->has('Content-Encoding') &&
            $this->shouldCompress($request, $response) &&
            !$this->isAjaxRequest($request)) {

            $content = $response->getContent();
            if (strlen($content) > 2048) { // Compresser seulement si > 2KB
                $compressed = gzencode($content, 6);
                if ($compressed !== false && strlen($compressed) < strlen($content)) {
                    $response->setContent($compressed);
                    $response->headers->set('Content-Encoding', 'gzip');
                    $response->headers->set('Content-Length', strlen($compressed));
                }
            }
        }

        // Headers de cache pour les assets statiques
        if ($this->isStaticAsset($request)) {
            $response->headers->set('Cache-Control', 'public, max-age=31536000, immutable');
            $response->headers->set('Expires', gmdate('D, d M Y H:i:s', time() + 31536000) . ' GMT');
        } else {
            // Headers de cache pour les pages dynamiques
            $response->headers->set('Cache-Control', 'no-cache, must-revalidate');
        }

        // Headers de sécurité (seulement en production)
        if (config('app.env') === 'production') {
            $response->headers->set('X-Content-Type-Options', 'nosniff');
            $response->headers->set('X-Frame-Options', 'DENY');
            $response->headers->set('X-XSS-Protection', '1; mode=block');
            $response->headers->set('Referrer-Policy', 'strict-origin-when-cross-origin');

            if ($request->secure()) {
                $response->headers->set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
            }
        }

        // Minification HTML seulement en production et pour les pages non-AJAX
        if ($this->shouldMinifyHtml($request, $response) && !$this->isAjaxRequest($request)) {
            $content = $response->getContent();
            $minified = $this->minifyHtml($content);
            $response->setContent($minified);
        }

        return $response;
    }

    /**
     * Détermine si la réponse doit être compressée
     */
    private function shouldCompress(Request $request, Response $response): bool
    {
        $contentType = $response->headers->get('Content-Type', '');
        
        $compressibleTypes = [
            'text/html',
            'text/css',
            'text/javascript',
            'application/javascript',
            'application/json',
            'application/xml',
            'text/xml',
            'text/plain'
        ];

        foreach ($compressibleTypes as $type) {
            if (strpos($contentType, $type) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Détermine si c'est un asset statique
     */
    private function isStaticAsset(Request $request): bool
    {
        $path = $request->getPathInfo();
        $extensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico', '.woff', '.woff2', '.ttf', '.eot'];
        
        foreach ($extensions as $ext) {
            if (str_ends_with($path, $ext)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Détermine si le HTML doit être minifié
     */
    private function shouldMinifyHtml(Request $request, Response $response): bool
    {
        if (config('app.env') !== 'production') {
            return false;
        }

        $contentType = $response->headers->get('Content-Type', '');
        return strpos($contentType, 'text/html') !== false;
    }

    /**
     * Vérifie si c'est une requête AJAX
     */
    private function isAjaxRequest(Request $request): bool
    {
        return $request->ajax() ||
               $request->wantsJson() ||
               $request->expectsJson() ||
               $request->header('X-Requested-With') === 'XMLHttpRequest';
    }

    /**
     * Minifie le contenu HTML
     */
    private function minifyHtml(string $html): string
    {
        // Ne pas minifier si le contenu contient des scripts ou du JSON
        if (strpos($html, '<script') !== false || strpos($html, 'application/json') !== false) {
            return $html;
        }

        // Supprimer les commentaires HTML (sauf les commentaires conditionnels IE)
        $html = preg_replace('/<!--(?!\s*(?:\[if [^\]]+]|<!|>))(?:(?!-->).)*-->/s', '', $html);

        // Supprimer les espaces multiples (mais préserver les espaces dans les balises pre et textarea)
        $html = preg_replace_callback('/(<pre[^>]*>.*?<\/pre>|<textarea[^>]*>.*?<\/textarea>)/is', function($matches) {
            return $matches[0]; // Préserver le contenu original
        }, $html);

        $html = preg_replace('/\s+/', ' ', $html);

        // Supprimer les espaces autour des balises
        $html = preg_replace('/>\s+</', '><', $html);

        // Supprimer les espaces en début et fin
        $html = trim($html);

        return $html;
    }
}
