1754237758a:3:{s:6:"alerts";a:5:{s:18:"low_stock_products";i:5;s:16:"pending_supplies";i:0;s:21:"pending_cement_orders";i:0;s:16:"inactive_drivers";i:0;s:18:"overdue_deliveries";i:0;}s:10:"topDrivers";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:1:{i:0;O:17:"App\Models\Driver":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:7:"drivers";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:7:{s:2:"id";i:1;s:10:"first_name";s:8:"Awédéo";s:9:"last_name";s:11:"TCHASSIBAYA";s:9:"is_active";i:1;s:9:"full_name";s:20:"Awédéo TCHASSIBAYA";s:15:"completed_trips";i:6;s:20:"completed_deliveries";i:5;}s:11:" * original";a:5:{s:2:"id";i:1;s:10:"first_name";s:8:"Awédéo";s:9:"last_name";s:11:"TCHASSIBAYA";s:9:"is_active";i:1;s:9:"full_name";s:20:"Awédéo TCHASSIBAYA";}s:10:" * changes";a:0:{}s:8:" * casts";a:3:{s:14:"license_expiry";s:4:"date";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:1:{i:0;s:9:"full_name";}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:11:{i:0;s:10:"first_name";i:1;s:9:"last_name";i:2;s:5:"email";i:3;s:5:"phone";i:4;s:14:"license_number";i:5;s:14:"license_expiry";i:6;s:7:"address";i:7;s:5:"notes";i:8;s:6:"status";i:9;s:9:"is_active";i:10;s:8:"truck_id";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}s:16:"lowStockProducts";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:5:{i:0;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:1;s:4:"name";s:20:"CIMTOGO-CPJ 45-LOMÉ";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:11:" * original";a:5:{s:2:"id";i:1;s:4:"name";s:20:"CIMTOGO-CPJ 45-LOMÉ";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";O:19:"App\Models\Category":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:10:"categories";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:2:{s:2:"id";i:1;s:4:"name";s:6:"Ciment";}s:11:" * original";a:2:{s:2:"id";i:1;s:4:"name";s:6:"Ciment";}s:10:" * changes";a:0:{}s:8:" * casts";a:2:{s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:0:{}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:5:{i:0;s:4:"name";i:1;s:4:"slug";i:2;s:11:"description";i:3;s:9:"is_active";i:4;s:4:"type";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:1;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:2;s:4:"name";s:24:"CIMTOGO-CPJ 45-INTERIEUR";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:11:" * original";a:5:{s:2:"id";i:2;s:4:"name";s:24:"CIMTOGO-CPJ 45-INTERIEUR";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:111;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:2;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:3;s:4:"name";s:18:"CIMCO-CPJ 45-LOMÉ";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:11:" * original";a:5:{s:2:"id";i:3;s:4:"name";s:18:"CIMCO-CPJ 45-LOMÉ";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:111;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:3;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:4;s:4:"name";s:19:"CIMCO-CPJ 45-SAVANE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:11:" * original";a:5:{s:2:"id";i:4;s:4:"name";s:19:"CIMCO-CPJ 45-SAVANE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:111;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}i:4;O:18:"App\Models\Product":31:{s:13:" * connection";s:5:"mysql";s:8:" * table";s:8:"products";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:5:{s:2:"id";i:5;s:4:"name";s:21:"CIMCO-CPJ 45-CENTRALE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:11:" * original";a:5:{s:2:"id";i:5;s:4:"name";s:21:"CIMCO-CPJ 45-CENTRALE";s:11:"category_id";i:1;s:14:"stock_quantity";i:0;s:5:"price";s:4:"0.00";}s:10:" * changes";a:0:{}s:8:" * casts";a:4:{s:14:"stock_quantity";s:7:"integer";s:5:"price";s:5:"float";s:9:"is_active";s:7:"boolean";s:10:"deleted_at";s:8:"datetime";}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"category";r:111;}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:13:"usesUniqueIds";b:0;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:9:{i:0;s:4:"name";i:1;s:4:"code";i:2;s:4:"slug";i:3;s:11:"description";i:4;s:11:"category_id";i:5;s:4:"unit";i:6;s:14:"stock_quantity";i:7;s:5:"price";i:8;s:9:"is_active";}s:10:" * guarded";a:1:{i:0;s:1:"*";}s:16:" * forceDeleting";b:0;}}s:28:" * escapeWhenCastingToString";b:0;}}